# 环境变量配置示例
# Environment Variables Configuration Example

# OpenAI官方API配置（用于GPT对话、文本生成等）
# OpenAI Official API Configuration (for GPT chat, text generation, etc.)
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# 302.ai API配置（专门用于图片生成）
# 302.ai API Configuration (specifically for image generation)
API_302_KEY=sk-your-302ai-api-key-here

# 使用说明：
# 1. 复制此文件为 .env
# 2. 填入你的真实API密钥
# 3. 确保 .env 文件已添加到 .gitignore 中，避免泄露密钥

# Usage Instructions:
# 1. Copy this file as .env
# 2. Fill in your real API keys
# 3. Make sure .env file is added to .gitignore to avoid key leakage
