#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI图片生成服务
AI Image Generation Service

使用302.ai的Midjourney API生成队徽图片
"""

import os
import base64
import requests
import json
import time
from typing import Optional, Dict, Any
import streamlit as st
from datetime import datetime

from config.settings import app_settings
from utils.helpers import ensure_directory_exists


class AIImageGenerationService:
    """AI图片生成服务"""
    
    def __init__(self, user_id: str = None):
        self.user_id = user_id
        self.api_key = app_settings.ai.API_302_KEY
        self.base_url = app_settings.ai.API_302_BASE_URL
        
        # 设置用户专属的队徽保存路径
        if user_id:
            from services.auth_service import AuthService
            auth_service = AuthService()
            user_data_path = auth_service.get_user_data_path(user_id)
            self.logos_folder = os.path.join(user_data_path, "logos")
            ensure_directory_exists(self.logos_folder)
        else:
            self.logos_folder = "assets/logos"
            ensure_directory_exists(self.logos_folder)
    
    def generate_team_logo_image(self, team_name: str, logo_description: str, 
                                style: str = "现代", color_preference: str = "蓝色") -> Optional[str]:
        """
        生成球队队徽图片
        
        Args:
            team_name: 球队名称
            logo_description: 队徽描述
            style: 风格
            color_preference: 颜色偏好
            
        Returns:
            Optional[str]: 生成的队徽图片路径，失败返回None
        """
        try:
            # 1. 构建Midjourney提示词
            prompt = self._build_logo_prompt(team_name, logo_description, style, color_preference)
            
            # 2. 调用302.ai的OpenAI格式API（更稳定）
            image_path = self._generate_with_openai_format(prompt, team_name)
            
            if image_path:
                st.success(f"✅ 队徽生成成功: {os.path.basename(image_path)}")
                return image_path
            else:
                # 回退到Midjourney API
                st.info("🔄 尝试使用Midjourney API...")
                return self._generate_with_midjourney(prompt, team_name)
                
        except Exception as e:
            st.error(f"❌ 队徽生成失败: {e}")
            return None
    
    def _build_logo_prompt(self, team_name: str, logo_description: str, 
                          style: str, color_preference: str) -> str:
        """构建队徽生成提示词"""
        # 基础提示词
        base_prompt = f"""
        Create a professional football team logo for "{team_name}".
        Style: {style}
        Primary colors: {color_preference}
        
        Design requirements:
        - Clean and modern design
        - Suitable for sports team
        - High contrast and visibility
        - Professional appearance
        - Circular or shield shape preferred
        
        Based on description: {logo_description}
        
        --ar 1:1 --v 6 --style raw --quality 2
        """
        
        return base_prompt.strip()
    
    def _generate_with_openai_format(self, prompt: str, team_name: str) -> Optional[str]:
        """使用302.ai的OpenAI格式API生成图片"""
        try:
            url = f"{self.base_url}/302/images/generations"

            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            payload = {
                "model": "hidream-i1-fast",  # 使用快速模型
                "prompt": prompt,
                "n": 1,  # 生成1张图片，避免4宫格
                "size": "728x728",  # 更高分辨率，适合队徽
                "response_format": "b64_json",  # 使用base64格式
                "quality": "standard",
                "aspect_ratio": "1:1"  # 正方形比例，适合队徽
            }

            st.info(f"🎨 正在调用302.ai OpenAI格式API生成队徽...")
            response = requests.post(url, headers=headers, json=payload, timeout=60)

            st.info(f"📡 API响应状态: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                st.info(f"📋 API响应数据: {result}")

                if result.get("data") and len(result["data"]) > 0:
                    # 解码base64图片
                    image_data = result["data"][0]
                    # 根据API文档，正确的字段名是 bs62_json
                    b64_data = image_data.get("bs62_json") or image_data.get("b64_json") or image_data.get("bs64_json")

                    if b64_data:
                        image_bytes = base64.b64decode(b64_data)

                        # 保存图片
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        filename = f"{team_name}_logo_{timestamp}.png"
                        file_path = os.path.join(self.logos_folder, filename)

                        with open(file_path, "wb") as f:
                            f.write(image_bytes)

                        st.success(f"✅ OpenAI格式API生成成功: {filename}")
                        return file_path
                    else:
                        st.warning(f"⚠️ 响应中未找到base64数据: {image_data}")
                        st.info(f"🔍 可用字段: {list(image_data.keys())}")
                else:
                    st.warning(f"⚠️ 响应中无图片数据: {result}")
            else:
                st.warning(f"❌ OpenAI格式API响应异常: {response.status_code}, {response.text}")

            return None

        except Exception as e:
            st.warning(f"❌ OpenAI格式API调用失败: {e}")
            return None
    
    def _generate_with_midjourney(self, prompt: str, team_name: str) -> Optional[str]:
        """使用Midjourney API生成图片"""
        try:
            # 1. 提交生成任务
            task_id = self._submit_midjourney_task(prompt)
            if not task_id:
                return None
            
            # 2. 轮询任务状态
            image_url = self._poll_task_result(task_id)
            if not image_url:
                return None
            
            # 3. 下载并保存图片
            return self._download_and_save_image(image_url, team_name)
            
        except Exception as e:
            st.error(f"Midjourney API调用失败: {e}")
            return None
    
    def _submit_midjourney_task(self, prompt: str) -> Optional[str]:
        """提交Midjourney生成任务"""
        try:
            url = f"{self.base_url}/mj/submit/imagine"
            
            headers = {
                'mj-api-secret': self.api_key,
                'Content-Type': 'application/json'
            }
            
            payload = {
                "prompt": prompt,
                "botType": "MID_JOURNEY",
                "base64Array": [],
                "notifyHook": "",
                "state": ""
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 1:  # 提交成功
                    return result.get("result")  # 任务ID
            
            st.error(f"Midjourney任务提交失败: {response.text}")
            return None
            
        except Exception as e:
            st.error(f"提交Midjourney任务异常: {e}")
            return None
    
    def _poll_task_result(self, task_id: str, max_wait_time: int = 300) -> Optional[str]:
        """轮询任务结果"""
        try:
            # 根据常见的Midjourney API模式，任务查询通常是这样的URL
            url = f"{self.base_url}/mj/task/{task_id}/fetch"

            headers = {
                'mj-api-secret': self.api_key
            }

            start_time = time.time()
            st.info(f"🔄 开始轮询Midjourney任务状态: {task_id}")

            while time.time() - start_time < max_wait_time:
                response = requests.get(url, headers=headers, timeout=10)

                st.info(f"📡 任务查询响应: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    st.info(f"📋 任务状态响应: {result}")

                    # 检查任务状态
                    status = result.get("status")
                    if status == "SUCCESS":
                        # 任务完成，获取图片URL
                        image_url = result.get("imageUrl")
                        st.success(f"✅ Midjourney任务完成: {image_url}")
                        return image_url
                    elif status in ["FAILURE", "CANCELLED"]:
                        st.error(f"❌ Midjourney任务失败: {result.get('failReason', '未知错误')}")
                        return None
                    else:
                        # 任务进行中，继续等待
                        st.info(f"⏳ 任务进行中，状态: {status}")
                        time.sleep(10)
                        continue
                else:
                    st.warning(f"⚠️ 任务查询失败: {response.status_code}, {response.text}")

                time.sleep(5)

            st.error("❌ Midjourney任务超时")
            return None

        except Exception as e:
            st.error(f"❌ 轮询任务结果异常: {e}")
            return None
    
    def _download_and_save_image(self, image_url: str, team_name: str) -> Optional[str]:
        """下载并保存图片"""
        try:
            response = requests.get(image_url, timeout=30)
            
            if response.status_code == 200:
                # 生成文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{team_name}_logo_{timestamp}.png"
                file_path = os.path.join(self.logos_folder, filename)
                
                # 保存图片
                with open(file_path, "wb") as f:
                    f.write(response.content)
                
                return file_path
            else:
                st.error(f"下载图片失败: {response.status_code}")
                return None
                
        except Exception as e:
            st.error(f"下载图片异常: {e}")
            return None
    
    def get_team_logo_path(self, team_name: str) -> Optional[str]:
        """
        获取球队队徽路径
        
        Args:
            team_name: 球队名称
            
        Returns:
            Optional[str]: 队徽路径，如果不存在返回None
        """
        try:
            if not os.path.exists(self.logos_folder):
                return None
            
            # 查找该球队的队徽文件
            for filename in os.listdir(self.logos_folder):
                if filename.startswith(f"{team_name}_logo_") and filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    return os.path.join(self.logos_folder, filename)
            
            return None
            
        except Exception as e:
            st.error(f"获取队徽路径失败: {e}")
            return None
    
    def delete_team_logo(self, logo_path: str) -> bool:
        """
        删除队徽文件
        
        Args:
            logo_path: 队徽文件路径
            
        Returns:
            bool: 是否删除成功
        """
        try:
            if os.path.exists(logo_path) and logo_path.startswith(self.logos_folder):
                os.remove(logo_path)
                return True
            return False
        except Exception as e:
            st.error(f"删除队徽失败: {e}")
            return False
