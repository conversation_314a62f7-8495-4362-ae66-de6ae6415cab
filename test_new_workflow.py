#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的换装流程：背景去除 → 白底添加 → AI换装
"""

import os
import sys
import time
import shutil
from pathlib import Path

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入必要的模块
from services.fashion_api_service import FashionAPIService

def test_new_workflow():
    """测试新的换装工作流：背景去除 → 白底 → 换装"""
    print("🧪 测试新的换装流程")
    print("=" * 50)
    print("📋 新流程：背景去除 → 白底添加 → AI换装")
    
    # 初始化服务
    fashion_service = FashionAPIService()
    
    # 测试图片路径
    test_image = "data/user_a10693c387d7/templates/team_templates/11122/微信图片_2025-08-19_095124_909_20250823_162558.jpg"
    clothes_image = test_image  # 使用同一张图片作为衣服
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图片不存在: {test_image}")
        return
    
    print(f"📸 测试图片: {test_image}")
    print(f"👕 衣服图片: {clothes_image}")
    
    # 创建临时目录
    temp_dir = "test_new_workflow_temp"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    print(f"📁 临时目录: {temp_dir}")
    
    # 设置临时目录
    fashion_service.temp_dir = temp_dir
    
    print("\n🎯 开始测试新的换装工作流...")
    print("💡 理论优势：")
    print("  - AI换装输入的是白底图片，可能效果更好")
    print("  - 如果AI保持背景，最终结果直接就是白底")
    print("  - 减少后处理步骤")
    
    start_time = time.time()
    
    # 执行完整工作流
    result = fashion_service.process_single_complete_workflow(test_image, clothes_image)
    
    end_time = time.time()
    
    print(f"\n📊 处理结果:")
    print(f"  - 成功: {result['success']}")
    print(f"  - 最终结果: {result['final_result']}")
    print(f"  - 处理时间: {end_time - start_time:.2f}秒")
    
    if result['error']:
        print(f"  - 错误: {result['error']}")
    
    print(f"\n📁 检查生成的文件:")
    if os.path.exists(temp_dir):
        files = os.listdir(temp_dir)
        if files:
            for file in files:
                file_path = os.path.join(temp_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"  - {file} ({file_size} bytes)")
        else:
            print("  - 临时目录为空")
    
    print(f"\n📋 步骤详情:")
    for step_name, step_info in result['steps'].items():
        status = "✅" if step_info['success'] else "❌"
        print(f"  - {step_name}: {status} {step_info.get('result_path', 'N/A')}")
    
    print(f"\n🔍 流程分析:")
    if result['success']:
        print("✅ 新流程测试成功！")
        print("📝 请检查最终结果图片是否为白底效果")
        print("💡 如果AI换装保持了白底背景，这个流程就是最优的")
    else:
        print("❌ 新流程测试失败")
        print("📝 可能需要回到之前的流程")

if __name__ == "__main__":
    try:
        test_new_workflow()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
