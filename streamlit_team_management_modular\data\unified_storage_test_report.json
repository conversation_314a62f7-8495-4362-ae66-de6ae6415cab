{"test_time": "2025-08-23T21:46:45.439466", "summary": {"total_tests": 28, "passed_tests": 28, "failed_tests": 0, "success_rate": 100.0}, "test_results": [{"timestamp": "21:46:45", "test_name": "测试环境设置", "success": true, "details": "创建用户目录: data\\test_unified_storage"}, {"timestamp": "21:46:45", "test_name": "FileManager.upload_folder", "success": true, "details": "期望: data\\test_unified_storage\\photos, 实际: data\\test_unified_storage\\photos"}, {"timestamp": "21:46:45", "test_name": "FileManager.processed_folder", "success": true, "details": "期望: data\\test_unified_storage\\processed_photos, 实际: data\\test_unified_storage\\processed_photos"}, {"timestamp": "21:46:45", "test_name": "FileManager.temp_fashion_folder", "success": true, "details": "期望: data\\test_unified_storage\\temp_fashion, 实际: data\\test_unified_storage\\temp_fashion"}, {"timestamp": "21:46:45", "test_name": "FileManager.word_output_folder", "success": true, "details": "期望: data\\test_unified_storage\\word_output, 实际: data\\test_unified_storage\\word_output"}, {"timestamp": "21:46:45", "test_name": "FileManager.templates_folder", "success": true, "details": "期望: data\\test_unified_storage\\templates, 实际: data\\test_unified_storage\\templates"}, {"timestamp": "21:46:45", "test_name": "FileManager.logos_folder", "success": true, "details": "期望: data\\test_unified_storage\\logos, 实际: data\\test_unified_storage\\logos"}, {"timestamp": "21:46:45", "test_name": "ExportService.exports_folder", "success": true, "details": "期望: data\\test_unified_storage\\exports, 实际: data\\test_unified_storage\\exports"}, {"timestamp": "21:46:45", "test_name": "WordGenerator.output_dir", "success": true, "details": "期望: data\\test_unified_storage\\word_output, 实际: C:\\Users\\<USER>\\Desktop\\test\\comfyui\\00000\\2222\\streamlit_team_management_backup copy 2\\streamlit_team_management_modular\\data\\test_unified_storage\\word_output"}, {"timestamp": "21:46:45", "test_name": "照片文件保存", "success": true, "details": "保存位置: data\\test_unified_storage\\photos\\测试球队_统一存储\\test_player.jpg"}, {"timestamp": "21:46:45", "test_name": "处理后照片保存", "success": true, "details": "保存位置: data\\test_unified_storage\\processed_photos\\测试球队_统一存储\\processed_test_player.jpg"}, {"timestamp": "21:46:45", "test_name": "换装临时文件保存", "success": true, "details": "保存位置: data\\test_unified_storage\\temp_fashion\\测试球队_统一存储\\fashion_result.png"}, {"timestamp": "21:46:45", "test_name": "AI导出数据保存", "success": true, "details": "保存位置: data\\test_unified_storage\\exports\\team_测试球队_统一存储_ai_ready.json"}, {"timestamp": "21:46:45", "test_name": "全局目录 uploads 已清理", "success": true, "details": "目录不存在（已被清理）"}, {"timestamp": "21:46:45", "test_name": "全局目录 processed_photos 已清理", "success": true, "details": "目录不存在（已被清理）"}, {"timestamp": "21:46:45", "test_name": "全局目录 ai_export 已清理", "success": true, "details": "目录不存在（已被清理）"}, {"timestamp": "21:46:45", "test_name": "全局目录 word_output 已清理", "success": true, "details": "目录不存在（已被清理）"}, {"timestamp": "21:46:45", "test_name": "全局目录 temp_files 无新数据", "success": true, "details": "检查是否有测试期间的新文件"}, {"timestamp": "21:46:45", "test_name": "用户子目录 teams", "success": true, "details": "路径: data\\test_unified_storage\\teams"}, {"timestamp": "21:46:45", "test_name": "用户子目录 photos", "success": true, "details": "路径: data\\test_unified_storage\\photos"}, {"timestamp": "21:46:45", "test_name": "用户子目录 templates", "success": true, "details": "路径: data\\test_unified_storage\\templates"}, {"timestamp": "21:46:45", "test_name": "用户子目录 processed_photos", "success": true, "details": "路径: data\\test_unified_storage\\processed_photos"}, {"timestamp": "21:46:45", "test_name": "用户子目录 word_output", "success": true, "details": "路径: data\\test_unified_storage\\word_output"}, {"timestamp": "21:46:45", "test_name": "用户子目录 temp_fashion", "success": true, "details": "路径: data\\test_unified_storage\\temp_fashion"}, {"timestamp": "21:46:45", "test_name": "用户子目录 logos", "success": true, "details": "路径: data\\test_unified_storage\\logos"}, {"timestamp": "21:46:45", "test_name": "用户子目录 enhanced_ai_data", "success": true, "details": "路径: data\\test_unified_storage\\enhanced_ai_data"}, {"timestamp": "21:46:45", "test_name": "用户子目录 fashion_workflow", "success": true, "details": "路径: data\\test_unified_storage\\fashion_workflow"}, {"timestamp": "21:46:45", "test_name": "用户子目录 exports", "success": true, "details": "路径: data\\test_unified_storage\\exports"}]}