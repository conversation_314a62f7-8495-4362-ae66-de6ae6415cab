import java.util.List;
import java.util.Map;

/**
 * Python集成适配器
 * 专门用于处理从Python传递过来的数据
 * 提供简化的接口供JPype调用
 */
public class PythonIntegrationAdapter {
    private WordGeneratorCore wordGenerator;
    
    /**
     * 构造函数
     * @param templatePath Word模板路径
     * @param outputDirectory 输出目录
     * @param photosDirectory 照片目录
     */
    public PythonIntegrationAdapter(String templatePath, String outputDirectory, String photosDirectory) {
        this.wordGenerator = new WordGeneratorCore(templatePath, outputDirectory, photosDirectory);
        System.out.println("🔗 Python集成适配器已初始化");
        System.out.println("   模板路径: " + templatePath);
        System.out.println("   输出目录: " + outputDirectory);
        System.out.println("   照片目录: " + photosDirectory);
    }
    
    /**
     * 从Python数据生成Word报名表
     * 这是主要的Python调用接口
     * 
     * @param teamInfoMap 队伍信息Map (title, organizationName, teamLeader, coach, teamDoctor)
     * @param playersData 球员数据List，每个元素是Map (number, name, photoPath)
     * @return 生成的Word文件路径，失败返回null
     */
    public String generateReportFromPython(Map<String, String> teamInfoMap, List<Map<String, String>> playersData) {
        try {
            System.out.println("🐍 接收Python数据，开始生成Word报名表...");
            
            // 转换队伍信息
            TeamInfo teamInfo = convertTeamInfo(teamInfoMap);
            if (teamInfo == null) {
                System.err.println("❌ 队伍信息转换失败");
                return null;
            }
            
            // 转换球员数据
            PlayerData[] players = convertPlayersData(playersData);
            if (players == null) {
                System.err.println("❌ 球员数据转换失败");
                return null;
            }
            
            // 创建完整的队伍数据
            FootballTeamData teamData = FootballTeamData.fromPythonData(teamInfo, players);
            
            // 生成Word文档
            String result = wordGenerator.generateReport(teamData);
            
            if (result != null) {
                System.out.println("✅ Python集成成功，Word文档已生成：" + result);
            } else {
                System.err.println("❌ Python集成失败，Word文档生成失败");
            }
            
            return result;
            
        } catch (Exception e) {
            System.err.println("❌ Python集成适配器出错：" + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 简化的Python调用接口 - 使用默认参数
     * @param title 比赛标题
     * @param organizationName 单位名称
     * @param playersData 球员数据List
     * @return 生成的Word文件路径
     */
    public String generateSimpleReport(String title, String organizationName, List<Map<String, String>> playersData) {
        // 创建简化的队伍信息
        Map<String, String> teamInfoMap = new java.util.HashMap<>();
        teamInfoMap.put("title", title);
        teamInfoMap.put("organizationName", organizationName);
        teamInfoMap.put("teamLeader", "");
        teamInfoMap.put("coach", "");
        teamInfoMap.put("teamDoctor", "");
        
        return generateReportFromPython(teamInfoMap, playersData);
    }
    
    /**
     * 转换队伍信息
     */
    private TeamInfo convertTeamInfo(Map<String, String> teamInfoMap) {
        try {
            if (teamInfoMap == null || teamInfoMap.isEmpty()) {
                System.err.println("队伍信息为空");
                return null;
            }
            
            String title = teamInfoMap.getOrDefault("title", "足球比赛报名表");
            String organizationName = teamInfoMap.getOrDefault("organizationName", "");
            String teamLeader = teamInfoMap.getOrDefault("teamLeader", "");
            String coach = teamInfoMap.getOrDefault("coach", "");
            String teamDoctor = teamInfoMap.getOrDefault("teamDoctor", "");
            
            TeamInfo teamInfo = TeamInfo.fromPythonData(title, organizationName, teamLeader, coach, teamDoctor);
            
            System.out.println("✅ 队伍信息转换成功：" + teamInfo.toString());
            return teamInfo;
            
        } catch (Exception e) {
            System.err.println("❌ 队伍信息转换失败：" + e.getMessage());
            return null;
        }
    }
    
    /**
     * 转换球员数据
     */
    private PlayerData[] convertPlayersData(List<Map<String, String>> playersData) {
        try {
            if (playersData == null || playersData.isEmpty()) {
                System.err.println("球员数据为空");
                return null;
            }
            
            PlayerData[] players = new PlayerData[10]; // 最多10个球员
            int validPlayerCount = 0;
            
            for (int i = 0; i < Math.min(playersData.size(), 10); i++) {
                Map<String, String> playerMap = playersData.get(i);
                if (playerMap != null) {
                    String number = playerMap.getOrDefault("number", "");
                    String name = playerMap.getOrDefault("name", "");
                    String photoPath = playerMap.getOrDefault("photoPath", "");
                    
                    // 创建球员对象
                    PlayerData player = PlayerData.fromPythonData(number, name, photoPath);
                    
                    if (player.isValid()) {
                        players[i] = player;
                        validPlayerCount++;
                        System.out.println("✅ 球员转换成功：" + player.toString());
                    } else {
                        System.err.println("⚠️ 球员数据无效，跳过：" + playerMap);
                    }
                }
            }
            
            if (validPlayerCount == 0) {
                System.err.println("❌ 没有有效的球员数据");
                return null;
            }
            
            System.out.println("✅ 球员数据转换完成，有效球员数量：" + validPlayerCount);
            return players;
            
        } catch (Exception e) {
            System.err.println("❌ 球员数据转换失败：" + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 验证Python数据格式
     * @param teamInfoMap 队伍信息
     * @param playersData 球员数据
     * @return 验证结果
     */
    public boolean validatePythonData(Map<String, String> teamInfoMap, List<Map<String, String>> playersData) {
        try {
            // 验证队伍信息
            if (teamInfoMap == null || teamInfoMap.isEmpty()) {
                System.err.println("❌ 队伍信息为空");
                return false;
            }
            
            String title = teamInfoMap.get("title");
            String organizationName = teamInfoMap.get("organizationName");
            
            if (title == null || title.trim().isEmpty()) {
                System.err.println("❌ 比赛标题不能为空");
                return false;
            }
            
            if (organizationName == null || organizationName.trim().isEmpty()) {
                System.err.println("❌ 单位名称不能为空");
                return false;
            }
            
            // 验证球员数据
            if (playersData == null || playersData.isEmpty()) {
                System.err.println("❌ 球员数据为空");
                return false;
            }
            
            int validPlayerCount = 0;
            for (Map<String, String> playerMap : playersData) {
                if (playerMap != null) {
                    String number = playerMap.get("number");
                    String name = playerMap.get("name");
                    
                    if (number != null && !number.trim().isEmpty() && 
                        name != null && !name.trim().isEmpty()) {
                        validPlayerCount++;
                    }
                }
            }
            
            if (validPlayerCount == 0) {
                System.err.println("❌ 没有有效的球员数据");
                return false;
            }
            
            System.out.println("✅ Python数据验证通过，有效球员数量：" + validPlayerCount);
            return true;
            
        } catch (Exception e) {
            System.err.println("❌ Python数据验证失败：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取配置信息（用于调试）
     */
    public String getConfiguration() {
        return String.format("配置信息:\n  模板路径: %s\n  输出目录: %s\n  照片目录: %s",
                           wordGenerator.getTemplatePath(),
                           wordGenerator.getOutputDirectory(),
                           wordGenerator.getPhotosDirectory());
    }
    
    /**
     * 测试连接（用于验证JPype集成）
     */
    public String testConnection() {
        return "✅ Python集成适配器连接正常！当前时间：" + new java.util.Date();
    }
}
