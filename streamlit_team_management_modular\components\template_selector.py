#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板选择组件
Template Selector Component

提供模板选择、上传、管理的UI组件
"""

import os
from typing import Optional, Dict, Any, List
import streamlit as st
from PIL import Image

from services.template_service import TemplateService
from config.constants import UIConstants


class TemplateSelectorComponent:
    """模板选择组件"""
    
    def __init__(self, user_id: str = None):
        self.user_id = user_id
        self.template_service = TemplateService(user_id)
    
    def render_template_selector(self, team_name: str = None, 
                                show_upload: bool = True) -> Optional[str]:
        """
        渲染模板选择器
        
        Args:
            team_name: 球队名称
            show_upload: 是否显示上传功能
            
        Returns:
            Optional[str]: 选中的模板路径
        """
        st.markdown("### 🎨 选择换装模板")
        
        # 创建选项卡
        if show_upload:
            tab1, tab2, tab3 = st.tabs(["📦 系统模板", "👤 我的模板", "📤 上传新模板"])
        else:
            tab1, tab2 = st.tabs(["📦 系统模板", "👤 我的模板"])
        
        selected_template = None
        
        # 系统模板选项卡
        with tab1:
            selected_template = self._render_system_templates()
        
        # 用户模板选项卡
        with tab2:
            user_template = self._render_user_templates(team_name)
            if user_template:
                selected_template = user_template
        
        # 上传新模板选项卡
        if show_upload:
            with tab3:
                uploaded_template = self._render_template_upload(team_name)
                if uploaded_template:
                    selected_template = uploaded_template
        
        return selected_template
    
    def _render_system_templates(self) -> Optional[str]:
        """渲染系统模板选择"""
        system_templates = self.template_service.get_system_templates()
        
        if not any(system_templates.values()):
            st.info("📦 暂无系统默认模板，请上传自定义模板")
            return None
        
        selected_template = None
        
        for category, templates in system_templates.items():
            if not templates:
                continue
                
            category_names = {
                "football_jerseys": "⚽ 足球球衣",
                "casual_wear": "👕 休闲服装", 
                "formal_wear": "👔 正装"
            }
            
            st.markdown(f"#### {category_names.get(category, category)}")
            
            # 使用列布局显示模板
            cols = st.columns(3)
            for i, template in enumerate(templates):
                with cols[i % 3]:
                    try:
                        # 显示模板预览
                        img = Image.open(template["path"])
                        st.image(img, caption=template["name"], width=150)
                        
                        # 选择按钮
                        if st.button(f"选择 {template['name']}", 
                                   key=f"system_{category}_{i}",
                                   use_container_width=True):
                            selected_template = template["path"]
                            st.success(f"✅ 已选择: {template['name']}")
                            
                        # 复制到个人模板按钮
                        if self.user_id and st.button(f"复制到我的模板", 
                                                     key=f"copy_{category}_{i}",
                                                     use_container_width=True):
                            copied_path = self.template_service.copy_system_template_to_user(
                                template["path"]
                            )
                            if copied_path:
                                st.rerun()
                                
                    except Exception as e:
                        st.error(f"加载模板失败: {e}")
        
        return selected_template
    
    def _render_user_templates(self, team_name: str = None) -> Optional[str]:
        """渲染用户模板选择"""
        if not self.user_id:
            st.info("👤 请登录后查看个人模板")
            return None
        
        user_templates = self.template_service.get_user_templates(team_name)
        
        if not any(user_templates.values()):
            st.info("👤 暂无个人模板，请上传或从系统模板复制")
            return None
        
        selected_template = None
        
        # 个人模板
        if user_templates["personal"]:
            st.markdown("#### 📁 个人模板")
            cols = st.columns(3)
            for i, template in enumerate(user_templates["personal"]):
                with cols[i % 3]:
                    try:
                        img = Image.open(template["path"])
                        st.image(img, caption=template["name"], width=150)
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button(f"选择", 
                                       key=f"personal_{i}",
                                       use_container_width=True):
                                selected_template = template["path"]
                                st.success(f"✅ 已选择: {template['name']}")
                        
                        with col2:
                            if st.button(f"删除", 
                                       key=f"del_personal_{i}",
                                       use_container_width=True):
                                if self.template_service.delete_user_template(template["path"]):
                                    st.rerun()
                                    
                    except Exception as e:
                        st.error(f"加载模板失败: {e}")
        
        # 球队专用模板
        if team_name and user_templates["team"]:
            st.markdown(f"#### 🏆 {team_name} 专用模板")
            cols = st.columns(3)
            for i, template in enumerate(user_templates["team"]):
                with cols[i % 3]:
                    try:
                        img = Image.open(template["path"])
                        st.image(img, caption=template["name"], width=150)
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button(f"选择", 
                                       key=f"team_{i}",
                                       use_container_width=True):
                                selected_template = template["path"]
                                st.success(f"✅ 已选择: {template['name']}")
                        
                        with col2:
                            if st.button(f"删除", 
                                       key=f"del_team_{i}",
                                       use_container_width=True):
                                if self.template_service.delete_user_template(template["path"]):
                                    st.rerun()
                                    
                    except Exception as e:
                        st.error(f"加载模板失败: {e}")
        
        return selected_template
    
    def _render_template_upload(self, team_name: str = None) -> Optional[str]:
        """渲染模板上传"""
        if not self.user_id:
            st.info("👤 请登录后上传模板")
            return None
        
        st.markdown("#### 📤 上传新模板")
        
        # 模板类别选择
        category = st.radio(
            "模板类别",
            ["personal", "team"],
            format_func=lambda x: "📁 个人模板" if x == "personal" else f"🏆 {team_name} 专用模板" if team_name else "🏆 球队专用模板",
            horizontal=True
        )
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "选择模板图片",
            type=['png', 'jpg', 'jpeg'],
            help=UIConstants.HELP_TEXTS["template_upload"]
        )
        
        if uploaded_file is not None:
            # 显示预览
            col1, col2 = st.columns([1, 2])
            
            with col1:
                st.image(uploaded_file, caption="模板预览", width=200)
            
            with col2:
                st.markdown("**模板信息：**")
                st.write(f"📁 文件名: {uploaded_file.name}")
                st.write(f"📊 文件大小: {uploaded_file.size / 1024:.1f} KB")
                st.write(f"📂 保存类别: {'个人模板' if category == 'personal' else f'{team_name} 专用模板'}")
                
                # 保存按钮
                if st.button("💾 保存模板", use_container_width=True):
                    saved_path = self.template_service.save_user_template(
                        uploaded_file, uploaded_file.name,
                        team_name if category == "team" else None,
                        category
                    )

                    if saved_path:
                        st.success("✅ 模板保存成功！")
                        # 清除可能的缓存
                        if 'template_cache' in st.session_state:
                            del st.session_state.template_cache
                        # 刷新界面以显示新上传的模板
                        st.rerun()
                        return saved_path
        
        return None
    
    def render_simple_template_upload(self, team_name: str = None) -> Optional[str]:
        """
        渲染简单的模板上传（用于向后兼容）

        Args:
            team_name: 球队名称

        Returns:
            Optional[str]: 模板路径（统一返回路径字符串）
        """
        st.markdown("### 🎨 选择或上传模板图")

        # 使用选项卡提供选择和上传功能
        tab1, tab2 = st.tabs(["📦 选择模板", "📤 上传新模板"])

        selected_template_path = None

        # 选择现有模板
        with tab1:
            # 添加刷新按钮
            if st.button("🔄 刷新模板列表", help="如果看不到刚上传的模板，请点击刷新"):
                st.rerun()

            # 显示系统模板（简化版）
            system_templates = self.template_service.get_system_templates()
            user_templates = self.template_service.get_user_templates(team_name)

            all_templates = []

            # 添加系统模板
            for category, templates in system_templates.items():
                for template in templates:
                    template["source"] = "系统模板"
                    all_templates.append(template)

            # 添加用户模板
            for category, templates in user_templates.items():
                for template in templates:
                    template["source"] = "个人模板" if category == "personal" else "球队模板"
                    all_templates.append(template)

            if all_templates:
                # 使用selectbox选择模板
                template_options = ["请选择模板..."] + [f"{t['name']} ({t['source']})" for t in all_templates]

                # 使用唯一的key来避免缓存问题
                selectbox_key = f"template_select_{len(all_templates)}_{hash(str([t['path'] for t in all_templates]))}"

                selected_index = st.selectbox(
                    "选择模板",
                    range(len(template_options)),
                    format_func=lambda x: template_options[x],
                    key=selectbox_key
                )

                if selected_index > 0:
                    selected_template = all_templates[selected_index - 1]
                    selected_template_path = selected_template["path"]

                    # 显示预览
                    col1, col2 = st.columns([1, 2])
                    with col1:
                        try:
                            from PIL import Image
                            img = Image.open(selected_template_path)
                            st.image(img, caption="模板预览", width=200)
                        except Exception as e:
                            st.error(f"无法加载模板预览: {e}")

                    with col2:
                        st.success(f"✅ 已选择: {selected_template['name']}")
                        st.info(f"📂 来源: {selected_template['source']}")
            else:
                st.info("暂无可用模板，请上传新模板")

        # 上传新模板
        with tab2:
            uploaded_file = st.file_uploader(
                "选择模板图片",
                type=['png', 'jpg', 'jpeg'],
                help=UIConstants.HELP_TEXTS["template_upload"]
            )

            if uploaded_file is not None:
                col1, col2 = st.columns([1, 2])
                with col1:
                    st.image(uploaded_file, caption="模板预览", width=200)
                with col2:
                    st.info("✅ 模板图已选择")

                    # 保存按钮
                    if st.button("💾 保存并使用此模板", use_container_width=True):
                        if self.user_id:
                            saved_path = self.template_service.save_user_template(
                                uploaded_file, uploaded_file.name,
                                team_name, "team" if team_name else "personal"
                            )
                            if saved_path:
                                selected_template_path = saved_path
                                st.success("✅ 模板保存成功！")
                                # 清除可能的缓存
                                if 'template_cache' in st.session_state:
                                    del st.session_state.template_cache
                                # 刷新界面以显示新上传的模板
                                st.rerun()
                        else:
                            st.error("请先登录后再保存模板")

        return selected_template_path
