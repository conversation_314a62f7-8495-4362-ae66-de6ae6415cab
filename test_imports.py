#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入问题
"""

import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

print("🔧 开始测试导入...")

try:
    print("1. 测试基础导入...")
    import streamlit as st
    print("✅ streamlit导入成功")
    
    print("2. 测试配置导入...")
    from config.settings import app_settings
    print("✅ app_settings导入成功")
    
    print("3. 测试服务导入...")
    from services.team_service import TeamService
    print("✅ TeamService导入成功")
    
    from services.export_service import ExportService
    print("✅ ExportService导入成功")
    
    print("4. 测试配置常量导入...")
    from config.constants import UIConstants
    print("✅ UIConstants导入成功")
    
    print("5. 测试sidebar组件导入...")
    from components.sidebar import SidebarComponent
    print("✅ SidebarComponent导入成功")
    
    print("6. 测试其他组件导入...")
    from components.player_form import PlayerFormComponent
    print("✅ PlayerFormComponent导入成功")
    
    from components.batch_upload import BatchUploadComponent
    print("✅ BatchUploadComponent导入成功")
    
    from components.photo_processing import PhotoProcessingComponent
    print("✅ PhotoProcessingComponent导入成功")
    
    from components.ai_chat import AIChatComponent
    print("✅ AIChatComponent导入成功")
    
    from components.player_list import PlayerListComponent
    print("✅ PlayerListComponent导入成功")
    
    from components.auth_component import AuthComponent
    print("✅ AuthComponent导入成功")
    
    from components.loading_manager import loading_manager
    print("✅ loading_manager导入成功")
    
    print("7. 测试工具导入...")
    from utils.smart_cache_manager import smart_cache
    print("✅ smart_cache导入成功")
    
    print("🎉 所有导入测试成功！")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
