#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单模板测试
Simple Template Test
"""

import streamlit as st
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from components.template_selector import TemplateSelectorComponent

def main():
    st.title("🎨 模板选择测试")
    
    # 设置用户ID
    if 'user_id' not in st.session_state:
        st.session_state.user_id = 'test_user'
    
    user_id = st.session_state.user_id
    team_name = "测试球队"
    
    st.write(f"用户ID: {user_id}")
    st.write(f"球队名称: {team_name}")
    
    # 创建模板选择器
    template_selector = TemplateSelectorComponent(user_id)
    
    # 渲染模板选择器
    selected_template = template_selector.render_simple_template_upload(team_name)
    
    if selected_template:
        st.success(f"已选择模板: {os.path.basename(selected_template)}")
        st.write(f"模板路径: {selected_template}")

if __name__ == "__main__":
    st.set_page_config(page_title="模板测试", page_icon="🎨")
    main()
