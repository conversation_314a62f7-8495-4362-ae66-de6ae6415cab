#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI自动状态感知功能
Test Auto AI State Sensing

验证AI能否自动感知球队状态变化
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from components.ai_chat import AIChatComponent
from services.team_service import TeamService
from services.player_service import PlayerService

def test_auto_ai_sensing():
    """测试AI自动状态感知功能"""
    print("🧪 开始测试AI自动状态感知功能...")
    
    # 初始化组件和服务
    ai_chat = AIChatComponent()
    team_service = TeamService()
    player_service = PlayerService()
    
    test_team = "测试自动感知球队"
    
    # 模拟不同的球队状态变化
    test_scenarios = [
        {
            "name": "初始状态 - 空球队",
            "stats": {
                'total_players': 0,
                'players_with_photos': 0,
                'completion_rate': 0.0,
                'is_complete': False
            }
        },
        {
            "name": "添加第一个球员",
            "stats": {
                'total_players': 1,
                'players_with_photos': 1,
                'completion_rate': 100.0,
                'is_complete': True
            }
        },
        {
            "name": "添加更多球员",
            "stats": {
                'total_players': 3,
                'players_with_photos': 2,
                'completion_rate': 66.7,
                'is_complete': False
            }
        },
        {
            "name": "完成所有球员信息",
            "stats": {
                'total_players': 5,
                'players_with_photos': 5,
                'completion_rate': 100.0,
                'is_complete': True
            }
        }
    ]
    
    print(f"\n🏆 测试球队：{test_team}")
    print("=" * 60)
    
    last_stats = {}
    
    for i, scenario in enumerate(test_scenarios):
        print(f"\n📊 场景 {i+1}：{scenario['name']}")
        print("-" * 40)
        
        current_stats = scenario['stats']
        
        # 测试状态变化检测
        has_changed = ai_chat._has_team_stats_changed(last_stats, current_stats)
        print(f"🔍 状态是否变化：{'是' if has_changed else '否'}")
        
        if has_changed:
            # 测试自动状态更新消息生成
            update_message = ai_chat._generate_auto_status_update(current_stats)
            if update_message:
                print(f"🤖 AI自动更新消息：")
                print(f"   {update_message}")
            else:
                print("🤖 无需生成更新消息")
        
        # 测试智能建议
        suggestions = ai_chat.ai_service._generate_smart_suggestions(current_stats)
        print(f"💡 智能建议：{suggestions}")
        
        # 测试状态描述
        status_desc = ai_chat.ai_service._get_team_status_description(current_stats)
        print(f"📈 状态描述：{status_desc}")
        
        last_stats = current_stats.copy()
    
    print("\n🎉 AI自动状态感知功能测试完成！")
    print("\n📝 测试总结：")
    print("- ✅ AI能够自动检测球队状态变化")
    print("- ✅ 状态变化时会生成相应的更新消息")
    print("- ✅ 智能建议会根据当前状态动态调整")
    print("- ✅ 用户无需手动刷新，AI自动感知")
    
    print("\n🚀 使用说明：")
    print("1. 创建球队后，AI会显示初始状态感知")
    print("2. 添加球员后，AI会自动感知并更新建议")
    print("3. 上传照片后，AI会自动更新完成度感知")
    print("4. 无需点击任何刷新按钮，一切都是自动的！")

if __name__ == "__main__":
    test_auto_ai_sensing()
