#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移脚本 - 统一到data目录结构
Data Migration Script - Unify to data directory structure

将分散在各个目录的用户数据迁移到统一的data/user_xxx/目录结构中
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any


class DataMigrationTool:
    """数据迁移工具"""
    
    def __init__(self):
        self.base_dir = Path(".")
        self.data_dir = self.base_dir / "data"
        self.migration_log = []
        
        # 旧的分散目录
        self.old_directories = {
            "uploads": self.base_dir / "uploads",
            "processed_photos": self.base_dir / "processed_photos", 
            "ai_export": self.base_dir / "ai_export",
            "word_output": self.base_dir / "word_output"
        }
    
    def log_action(self, action: str, details: str = ""):
        """记录迁移操作"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {action}"
        if details:
            log_entry += f" - {details}"
        
        self.migration_log.append(log_entry)
        print(log_entry)
    
    def discover_users(self) -> List[str]:
        """发现所有用户ID"""
        users = set()
        
        # 从各个旧目录中发现用户
        for dir_name, dir_path in self.old_directories.items():
            if dir_path.exists():
                for item in dir_path.iterdir():
                    if item.is_dir() and item.name.startswith("user_"):
                        users.add(item.name)
        
        # 从data目录中发现已存在的用户
        if self.data_dir.exists():
            for item in self.data_dir.iterdir():
                if item.is_dir() and item.name.startswith("user_"):
                    users.add(item.name)
        
        return sorted(list(users))
    
    def ensure_user_structure(self, user_id: str):
        """确保用户目录结构存在"""
        user_dir = self.data_dir / user_id
        
        # 创建用户主目录
        user_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        subdirs = [
            "teams", "photos", "templates", "processed_photos", 
            "word_output", "temp_fashion", "logos", 
            "enhanced_ai_data", "fashion_workflow"
        ]
        
        for subdir in subdirs:
            (user_dir / subdir).mkdir(exist_ok=True)
        
        self.log_action(f"确保用户目录结构", f"用户: {user_id}")
    
    def migrate_user_uploads(self, user_id: str):
        """迁移用户上传文件"""
        old_uploads = self.old_directories["uploads"] / user_id
        new_photos = self.data_dir / user_id / "photos"
        
        if old_uploads.exists():
            self._copy_directory_contents(old_uploads, new_photos)
            self.log_action(f"迁移上传文件", f"{old_uploads} -> {new_photos}")
    
    def migrate_user_processed_photos(self, user_id: str):
        """迁移用户处理后的照片"""
        old_processed = self.old_directories["processed_photos"] / user_id
        new_processed = self.data_dir / user_id / "processed_photos"
        
        if old_processed.exists():
            self._copy_directory_contents(old_processed, new_processed)
            self.log_action(f"迁移处理后照片", f"{old_processed} -> {new_processed}")
    
    def migrate_user_ai_export(self, user_id: str):
        """迁移用户AI导出数据（临时保留，后续可删除）"""
        old_ai_export = self.old_directories["ai_export"] / user_id
        new_exports = self.data_dir / user_id / "exports"
        
        # 创建exports目录（临时）
        new_exports.mkdir(exist_ok=True)
        
        if old_ai_export.exists():
            self._copy_directory_contents(old_ai_export, new_exports)
            self.log_action(f"迁移AI导出数据", f"{old_ai_export} -> {new_exports}")
    
    def migrate_user_word_output(self, user_id: str):
        """迁移用户Word输出文件"""
        old_word_output = self.old_directories["word_output"]
        new_word_output = self.data_dir / user_id / "word_output"
        
        if old_word_output.exists():
            # 查找属于该用户的Word文件（通过文件名或时间戳判断）
            for file_path in old_word_output.glob("*.docx"):
                # 简单策略：将所有Word文件复制到每个用户目录
                # 实际使用中可以根据文件名或其他标识符进行更精确的分配
                try:
                    shutil.copy2(file_path, new_word_output)
                    self.log_action(f"迁移Word文件", f"{file_path.name} -> {user_id}/word_output/")
                except Exception as e:
                    self.log_action(f"迁移Word文件失败", f"{file_path.name}: {e}")
    
    def _copy_directory_contents(self, source: Path, destination: Path):
        """复制目录内容"""
        if not source.exists():
            return
        
        destination.mkdir(parents=True, exist_ok=True)
        
        for item in source.rglob("*"):
            if item.is_file():
                # 计算相对路径
                relative_path = item.relative_to(source)
                dest_file = destination / relative_path
                
                # 确保目标目录存在
                dest_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                try:
                    shutil.copy2(item, dest_file)
                except Exception as e:
                    self.log_action(f"文件复制失败", f"{item} -> {dest_file}: {e}")
    
    def migrate_global_temp_files(self):
        """迁移全局临时文件到默认用户"""
        temp_files_dir = self.base_dir / "temp_files"
        
        if temp_files_dir.exists():
            # 将temp_files内容移动到default_user的temp_fashion目录
            default_temp = self.data_dir / "default_user" / "temp_fashion"
            default_temp.mkdir(parents=True, exist_ok=True)
            
            self._copy_directory_contents(temp_files_dir, default_temp)
            self.log_action(f"迁移全局临时文件", f"temp_files -> default_user/temp_fashion")
    
    def run_migration(self):
        """执行完整的数据迁移"""
        self.log_action("开始数据迁移", "统一到data目录结构")
        
        # 1. 发现所有用户
        users = self.discover_users()
        self.log_action(f"发现用户", f"共 {len(users)} 个用户: {', '.join(users)}")
        
        # 2. 为每个用户迁移数据
        for user_id in users:
            self.log_action(f"开始迁移用户数据", user_id)
            
            # 确保用户目录结构
            self.ensure_user_structure(user_id)
            
            # 迁移各类数据
            self.migrate_user_uploads(user_id)
            self.migrate_user_processed_photos(user_id)
            self.migrate_user_ai_export(user_id)
            self.migrate_user_word_output(user_id)
            
            self.log_action(f"完成用户数据迁移", user_id)
        
        # 3. 迁移全局临时文件
        self.migrate_global_temp_files()
        
        # 4. 保存迁移日志
        self.save_migration_log()
        
        self.log_action("数据迁移完成", "所有用户数据已统一到data目录")
    
    def save_migration_log(self):
        """保存迁移日志"""
        log_file = self.data_dir / "migration_log.txt"
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("数据迁移日志\n")
            f.write("=" * 50 + "\n")
            f.write(f"迁移时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"迁移项目: 统一用户数据到data目录结构\n\n")
            
            for log_entry in self.migration_log:
                f.write(log_entry + "\n")
        
        self.log_action(f"保存迁移日志", str(log_file))
    
    def create_backup_info(self):
        """创建备份信息文件"""
        backup_info = {
            "backup_time": datetime.now().isoformat(),
            "original_directories": {
                name: str(path) for name, path in self.old_directories.items()
            },
            "migration_status": "completed",
            "notes": [
                "原始目录已保留，可以手动删除",
                "exports目录为临时保留，后续可改为动态生成",
                "所有用户数据已统一到data/user_xxx/目录结构"
            ]
        }
        
        backup_file = self.data_dir / "backup_info.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, ensure_ascii=False, indent=2)
        
        self.log_action(f"创建备份信息", str(backup_file))


def main():
    """主函数"""
    print("🔄 数据迁移工具 - 统一到data目录结构")
    print("=" * 50)
    
    # 确认迁移
    response = input("是否开始数据迁移？这将把分散的用户数据统一到data目录 (y/N): ")
    if response.lower() != 'y':
        print("❌ 迁移已取消")
        return
    
    # 执行迁移
    migration_tool = DataMigrationTool()
    migration_tool.run_migration()
    migration_tool.create_backup_info()
    
    print("\n✅ 数据迁移完成！")
    print("📁 所有用户数据已统一到 data/user_xxx/ 目录结构")
    print("📋 迁移日志已保存到 data/migration_log.txt")
    print("💡 原始目录已保留，确认迁移成功后可手动删除")


if __name__ == "__main__":
    main()
