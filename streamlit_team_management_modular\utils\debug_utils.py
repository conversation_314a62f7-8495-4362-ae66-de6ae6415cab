#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试工具类
Debug Utilities

控制调试信息的显示，减少界面上的冗余信息
"""

import streamlit as st
from typing import Any, Optional
from config.settings import app_settings


class DebugManager:
    """调试信息管理器"""
    
    @staticmethod
    def info(message: str, show_always: bool = False) -> None:
        """显示信息消息"""
        if show_always or app_settings.DEBUG_MODE:
            st.info(message)
    
    @staticmethod
    def success(message: str, show_always: bool = True) -> None:
        """显示成功消息"""
        if show_always or app_settings.DEBUG_MODE:
            st.success(message)
    
    @staticmethod
    def warning(message: str, show_always: bool = True) -> None:
        """显示警告消息"""
        if show_always or app_settings.DEBUG_MODE:
            st.warning(message)
    
    @staticmethod
    def error(message: str, show_always: bool = True) -> None:
        """显示错误消息"""
        if show_always or app_settings.DEBUG_MODE:
            st.error(message)
    
    @staticmethod
    def detailed_info(message: str) -> None:
        """显示详细信息（仅在详细日志模式下显示）"""
        if app_settings.SHOW_DETAILED_LOGS:
            st.info(message)
    
    @staticmethod
    def file_path_info(message: str) -> None:
        """显示文件路径信息（仅在文件路径模式下显示）"""
        if app_settings.SHOW_FILE_PATHS:
            st.info(message)
    
    @staticmethod
    def progress_info(message: str) -> None:
        """显示进度信息（总是显示，但样式简化）"""
        st.info(message)
    
    @staticmethod
    def user_action_success(message: str) -> None:
        """用户操作成功消息（总是显示）"""
        st.success(message)
    
    @staticmethod
    def user_action_error(message: str) -> None:
        """用户操作错误消息（总是显示）"""
        st.error(message)
    
    @staticmethod
    def user_action_warning(message: str) -> None:
        """用户操作警告消息（总是显示）"""
        st.warning(message)


# 创建全局实例
debug = DebugManager()


def enable_debug_mode():
    """启用调试模式"""
    app_settings.DEBUG_MODE = True
    app_settings.SHOW_DETAILED_LOGS = True
    app_settings.SHOW_FILE_PATHS = True


def disable_debug_mode():
    """禁用调试模式"""
    app_settings.DEBUG_MODE = False
    app_settings.SHOW_DETAILED_LOGS = False
    app_settings.SHOW_FILE_PATHS = False


def toggle_debug_mode():
    """切换调试模式"""
    if app_settings.DEBUG_MODE:
        disable_debug_mode()
    else:
        enable_debug_mode()


def render_debug_controls():
    """渲染调试控制面板"""
    with st.expander("🔧 调试控制", expanded=False):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🐛 启用调试"):
                enable_debug_mode()
                st.success("调试模式已启用")
                st.rerun()
        
        with col2:
            if st.button("🔇 禁用调试"):
                disable_debug_mode()
                st.success("调试模式已禁用")
                st.rerun()
        
        with col3:
            st.write(f"当前状态: {'🟢 开启' if app_settings.DEBUG_MODE else '🔴 关闭'}")
        
        # 显示当前设置
        st.markdown("**当前设置:**")
        st.write(f"- 调试模式: {app_settings.DEBUG_MODE}")
        st.write(f"- 详细日志: {app_settings.SHOW_DETAILED_LOGS}")
        st.write(f"- 文件路径: {app_settings.SHOW_FILE_PATHS}")
