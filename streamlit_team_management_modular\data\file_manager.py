#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理器
File Manager

负责文件的上传、存储和管理
"""

import os
import shutil
from typing import Optional, List
import streamlit as st

from config.settings import app_settings
from utils.helpers import get_safe_team_name, ensure_directory_exists
from utils.image_utils import ImageProcessor


class FileManager:
    """文件管理器 - 统一使用data目录结构"""

    def __init__(self, user_id: str = None):
        self.user_id = user_id  # 保存user_id以供其他方法使用

        if user_id:
            # 用户专属的data目录结构
            user_data_path = app_settings.paths.get_user_data_path(user_id)
            self.upload_folder = os.path.join(user_data_path, "photos")
            self.processed_folder = os.path.join(user_data_path, "processed_photos")
            self.temp_fashion_folder = os.path.join(user_data_path, "temp_fashion")
            self.word_output_folder = os.path.join(user_data_path, "word_output")
            self.templates_folder = os.path.join(user_data_path, "templates")
            self.logos_folder = os.path.join(user_data_path, "logos")
        else:
            # 默认用户或系统级目录
            default_user_path = app_settings.paths.get_user_data_path("default_user")
            self.upload_folder = os.path.join(default_user_path, "photos")
            self.processed_folder = os.path.join(default_user_path, "processed_photos")
            self.temp_fashion_folder = os.path.join(default_user_path, "temp_fashion")
            self.word_output_folder = os.path.join(default_user_path, "word_output")
            self.templates_folder = os.path.join(default_user_path, "templates")
            self.logos_folder = os.path.join(default_user_path, "logos")

        # 确保目录存在
        for folder in [self.upload_folder, self.processed_folder, self.temp_fashion_folder,
                      self.word_output_folder, self.templates_folder, self.logos_folder]:
            ensure_directory_exists(folder)
    
    def get_team_upload_folder(self, team_name: str) -> str:
        """
        获取球队专属的上传文件夹（在用户photos目录下）

        Args:
            team_name: 球队名称

        Returns:
            str: 球队上传文件夹路径
        """
        safe_name = get_safe_team_name(team_name)
        team_folder = os.path.join(self.upload_folder, safe_name)
        ensure_directory_exists(team_folder)
        return team_folder

    def get_team_processed_folder(self, team_name: str) -> str:
        """
        获取球队专属的处理后照片文件夹

        Args:
            team_name: 球队名称

        Returns:
            str: 球队处理后照片文件夹路径
        """
        safe_name = get_safe_team_name(team_name)
        team_folder = os.path.join(self.processed_folder, safe_name)
        ensure_directory_exists(team_folder)
        return team_folder

    def get_team_temp_fashion_folder(self, team_name: str) -> str:
        """
        获取球队专属的换装临时文件夹

        Args:
            team_name: 球队名称

        Returns:
            str: 球队换装临时文件夹路径
        """
        safe_name = get_safe_team_name(team_name)
        team_folder = os.path.join(self.temp_fashion_folder, safe_name)
        ensure_directory_exists(team_folder)
        return team_folder
    
    def save_uploaded_photo(self, team_name: str, photo_file, filename: str) -> Optional[str]:
        """
        保存上传的照片
        
        Args:
            team_name: 球队名称
            photo_file: 上传的照片文件
            filename: 文件名
            
        Returns:
            Optional[str]: 保存的文件路径，如果失败返回None
        """
        try:
            team_folder = self.get_team_upload_folder(team_name)
            file_path = os.path.join(team_folder, filename)
            
            # 处理并保存图片
            if ImageProcessor.process_uploaded_image(
                photo_file, file_path, app_settings.MAX_IMAGE_SIZE
            ):
                return file_path
            else:
                return None
        except Exception as e:
            st.error(f"保存照片失败: {e}")
            return None
    
    def delete_photo(self, team_name: str, filename: str) -> bool:
        """
        删除照片文件
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            bool: 是否删除成功
        """
        try:
            team_folder = self.get_team_upload_folder(team_name)
            file_path = os.path.join(team_folder, filename)
            
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except Exception as e:
            st.error(f"删除照片失败: {e}")
            return False
    
    def get_photo_path(self, team_name: str, filename: str) -> Optional[str]:
        """
        获取照片完整路径
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            Optional[str]: 照片路径，如果不存在返回None
        """
        if not filename:
            return None
        
        team_folder = self.get_team_upload_folder(team_name)
        file_path = os.path.join(team_folder, filename)
        
        return file_path if os.path.exists(file_path) else None
    
    def photo_exists(self, team_name: str, filename: str) -> bool:
        """
        检查照片是否存在
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            bool: 照片是否存在
        """
        return self.get_photo_path(team_name, filename) is not None
    
    def list_team_photos(self, team_name: str) -> List[str]:
        """
        列出球队所有照片
        
        Args:
            team_name: 球队名称
            
        Returns:
            List[str]: 照片文件名列表
        """
        team_folder = self.get_team_upload_folder(team_name)
        
        if not os.path.exists(team_folder):
            return []
        
        photos = []
        for filename in os.listdir(team_folder):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                photos.append(filename)
        
        return photos
    
    def save_template_image(self, template_file, filename: str, team_name: str = None) -> Optional[str]:
        """
        保存模板图片（使用新的模板管理系统）

        Args:
            template_file: 模板文件或文件路径字符串
            filename: 文件名
            team_name: 球队名称（可选，用于球队专用模板）

        Returns:
            Optional[str]: 保存的文件路径，如果失败返回None
        """
        try:
            # 如果传入的是字符串路径，直接返回（避免重复保存）
            if isinstance(template_file, str):
                if os.path.exists(template_file):
                    st.info(f"✅ 使用现有模板: {os.path.basename(template_file)}")
                    return template_file
                else:
                    st.error(f"❌ 模板文件不存在: {template_file}")
                    return None

            # 如果是文件对象，使用新的模板管理服务保存
            from services.template_service import TemplateService
            template_service = TemplateService(self.user_id)

            # 确定模板类别
            category = "team" if team_name else "personal"

            # 保存模板
            return template_service.save_user_template(
                template_file, filename, team_name, category
            )

        except Exception as e:
            st.error(f"保存模板图片失败: {e}")
            # 回退到旧的保存方式（向后兼容）
            try:
                # 只有在template_file是文件对象时才尝试旧方式
                if hasattr(template_file, 'getvalue'):
                    templates_folder = os.path.join(self.processed_folder, "templates")
                    ensure_directory_exists(templates_folder)

                    file_path = os.path.join(templates_folder, filename)

                    # 保存文件
                    with open(file_path, "wb") as f:
                        f.write(template_file.getvalue())

                    return file_path
                else:
                    return None
            except Exception as e2:
                st.error(f"模板保存完全失败: {e2}")
                return None
    
    def cleanup_team_files(self, team_name: str) -> bool:
        """
        清理球队相关文件
        
        Args:
            team_name: 球队名称
            
        Returns:
            bool: 是否清理成功
        """
        try:
            team_folder = self.get_team_upload_folder(team_name)
            
            if os.path.exists(team_folder):
                shutil.rmtree(team_folder)
            
            return True
        except Exception as e:
            st.error(f"清理球队文件失败: {e}")
            return False
    
    def get_file_size(self, team_name: str, filename: str) -> Optional[int]:
        """
        获取文件大小
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            Optional[int]: 文件大小（字节），如果文件不存在返回None
        """
        file_path = self.get_photo_path(team_name, filename)
        
        if file_path and os.path.exists(file_path):
            return os.path.getsize(file_path)
        
        return None
