#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的换装功能
Test Fixed Fashion Try-on Functionality
"""

import os
import sys
import streamlit as st

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'streamlit_team_management_modular'))

from services.fashion_api_service import FashionAPIService

def test_fixed_fashion_workflow():
    """测试修复后的换装工作流"""
    
    print("🧪 测试修复后的换装功能")
    print("=" * 50)
    
    # 初始化服务
    fashion_service = FashionAPIService()
    
    if not fashion_service.is_available():
        print("❌ 换装API服务不可用，请检查配置")
        return
    
    # 测试图片路径
    test_image = r"streamlit_team_management_modular\data\user_a10693c387d7\templates\team_templates\11122\微信图片_2025-08-19_095124_909_20250823_162558.jpg"
    clothes_image = r"streamlit_team_management_modular\data\user_a10693c387d7\templates\team_templates\11122\微信图片_2025-08-19_095124_909_20250823_162558.jpg"  # 使用同一张图片作为衣服
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图片不存在: {test_image}")
        return
    
    print(f"📸 测试图片: {test_image}")
    print(f"👕 衣服图片: {clothes_image}")
    
    # 设置临时目录
    test_temp_dir = "test_fixed_temp"
    os.makedirs(test_temp_dir, exist_ok=True)
    fashion_service.temp_dir = test_temp_dir
    
    print(f"📁 临时目录: {test_temp_dir}")
    
    # 测试完整工作流
    print("\n🎯 开始测试完整换装工作流...")
    
    try:
        result = fashion_service.process_single_complete_workflow(test_image, clothes_image)
        
        print("\n📊 处理结果:")
        print(f"  - 成功: {result.get('success', False)}")
        print(f"  - 最终结果: {result.get('final_result', 'None')}")
        print(f"  - 处理时间: {result.get('processing_time', 0):.2f}秒")
        
        if result.get('steps'):
            print("  - 步骤详情:")
            for step_name, step_info in result['steps'].items():
                print(f"    * {step_name}: {'✅' if step_info.get('success') else '❌'}")
                if step_info.get('result_path'):
                    print(f"      文件: {step_info['result_path']}")
        
        if result.get('error'):
            print(f"  - 错误: {result['error']}")
        
        # 检查生成的文件
        print("\n📁 检查生成的文件:")
        if os.path.exists(test_temp_dir):
            files = os.listdir(test_temp_dir)
            if files:
                for file in files:
                    file_path = os.path.join(test_temp_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"  - {file} ({file_size} bytes)")
            else:
                print("  - 临时目录为空")
        else:
            print("  - 临时目录不存在")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_batch_processing():
    """测试批量处理"""
    
    print("\n🧪 测试批量处理功能")
    print("=" * 30)
    
    # 初始化服务
    fashion_service = FashionAPIService()
    
    # 创建测试图片列表（使用同一张图片模拟多张）
    test_image = r"streamlit_team_management_modular\data\user_a10693c387d7\templates\team_templates\11122\微信图片_2025-08-19_095124_909_20250823_162558.jpg"
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图片不存在: {test_image}")
        return
    
    # 模拟3张图片的批量处理
    player_images = [test_image] * 3
    clothes_image = test_image
    
    # 设置临时目录
    test_temp_dir = "test_batch_temp"
    os.makedirs(test_temp_dir, exist_ok=True)
    fashion_service.temp_dir = test_temp_dir
    
    print(f"📁 批量处理临时目录: {test_temp_dir}")
    print(f"📸 处理图片数量: {len(player_images)}")
    
    try:
        result = fashion_service.process_batch_fashion_tryon(player_images, clothes_image)
        
        print("\n📊 批量处理结果:")
        print(f"  - 总数: {result.get('total_processed', 0)}")
        print(f"  - 成功: {result.get('successful_count', 0)}")
        print(f"  - 失败: {result.get('failed_count', 0)}")
        print(f"  - 总耗时: {result.get('total_processing_time', 0):.2f}秒")
        print(f"  - 平均耗时: {result.get('average_time_per_photo', 0):.2f}秒/张")
        
        # 检查每个结果的文件
        if result.get('results'):
            print("\n📁 各图片处理结果:")
            for i, res in enumerate(result['results']):
                print(f"  图片 {i+1}:")
                print(f"    - 成功: {'✅' if res.get('success') else '❌'}")
                if res.get('final_result'):
                    print(f"    - 文件: {res['final_result']}")
                if res.get('unique_filename'):
                    print(f"    - 唯一文件名: {res['unique_filename']}")
                if res.get('error'):
                    print(f"    - 错误: {res['error']}")
        
        # 检查生成的文件
        print("\n📁 检查批量生成的文件:")
        if os.path.exists(test_temp_dir):
            files = os.listdir(test_temp_dir)
            if files:
                for file in sorted(files):
                    file_path = os.path.join(test_temp_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"  - {file} ({file_size} bytes)")
            else:
                print("  - 临时目录为空")
        
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 模拟streamlit环境
    class MockStreamlit:
        def info(self, msg): print(f"ℹ️  {msg}")
        def success(self, msg): print(f"✅ {msg}")
        def error(self, msg): print(f"❌ {msg}")
        def warning(self, msg): print(f"⚠️  {msg}")
        def write(self, msg): print(f"📝 {msg}")
        def progress(self, value): return MockProgress()
        def empty(self): return MockEmpty()
    
    class MockProgress:
        def progress(self, value): pass
    
    class MockEmpty:
        def text(self, msg): print(f"📊 {msg}")
    
    # 替换streamlit
    import sys
    sys.modules['streamlit'] = MockStreamlit()
    
    # 运行测试
    test_fixed_fashion_workflow()
    test_batch_processing()
    
    print("\n🎉 测试完成！")
    print("请检查生成的临时文件，验证修复效果。")
