import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * JSON数据解析器
 * 解析Python传递的JSON数据，转换为Java对象
 */
public class JsonDataParser {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 从JSON文件解析足球队数据
     * @param jsonFilePath JSON文件路径
     * @return FootballTeamData对象
     */
    public static FootballTeamData parseFromJson(String jsonFilePath) {
        try {
            // 读取JSON文件
            JsonNode rootNode = objectMapper.readTree(new File(jsonFilePath));
            
            // 解析队伍信息
            TeamInfo teamInfo = parseTeamInfo(rootNode.get("teamInfo"));
            if (teamInfo == null) {
                System.err.println("ERROR:Failed to parse team info from JSON");
                return null;
            }
            
            // 解析球员信息
            PlayerData[] players = parsePlayers(rootNode.get("players"));
            if (players == null) {
                System.err.println("ERROR:Failed to parse players from JSON");
                return null;
            }
            
            // 创建完整的球队数据
            FootballTeamData teamData = FootballTeamData.fromPythonData(teamInfo, players);
            
            System.err.println("INFO:Parsed team data - " + teamData.getValidPlayersInfo());
            
            return teamData;
            
        } catch (IOException e) {
            System.err.println("ERROR:Failed to read JSON file: " + e.getMessage());
            return null;
        } catch (Exception e) {
            System.err.println("ERROR:Failed to parse JSON data: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 解析队伍信息
     */
    private static TeamInfo parseTeamInfo(JsonNode teamInfoNode) {
        try {
            if (teamInfoNode == null) {
                System.err.println("WARN:No team info found in JSON, using defaults");
                return TeamInfo.fromPythonData("足球比赛报名表", "足球队", "", "", "");
            }
            
            String title = getStringValue(teamInfoNode, "title", "足球比赛报名表");
            String organizationName = getStringValue(teamInfoNode, "organizationName", "足球队");
            String teamLeader = getStringValue(teamInfoNode, "teamLeader", "");
            String coach = getStringValue(teamInfoNode, "coach", "");
            String teamDoctor = getStringValue(teamInfoNode, "teamDoctor", "");
            
            TeamInfo teamInfo = TeamInfo.fromPythonData(title, organizationName, teamLeader, coach, teamDoctor);
            
            System.err.println("INFO:Team info parsed: " + teamInfo.toString());
            
            return teamInfo;
            
        } catch (Exception e) {
            System.err.println("ERROR:Failed to parse team info: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析球员信息
     */
    private static PlayerData[] parsePlayers(JsonNode playersNode) {
        try {
            if (playersNode == null || !playersNode.isArray()) {
                System.err.println("WARN:No players array found in JSON");
                return new PlayerData[10]; // 返回空数组
            }
            
            List<PlayerData> playerList = new ArrayList<>();
            
            for (JsonNode playerNode : playersNode) {
                String number = getStringValue(playerNode, "number", "");
                String name = getStringValue(playerNode, "name", "");
                String photoPath = getStringValue(playerNode, "photoPath", "");
                
                if (!number.isEmpty() && !name.isEmpty()) {
                    PlayerData player = PlayerData.fromPythonData(number, name, photoPath);
                    playerList.add(player);
                    System.err.println("INFO:Player parsed: " + player.toString());
                } else {
                    System.err.println("WARN:Skipping invalid player data: number=" + number + ", name=" + name);
                }
            }
            
            // 转换为固定大小数组
            PlayerData[] players = new PlayerData[10];
            for (int i = 0; i < Math.min(playerList.size(), 10); i++) {
                players[i] = playerList.get(i);
            }
            
            System.err.println("INFO:Total players parsed: " + playerList.size());
            
            return players;
            
        } catch (Exception e) {
            System.err.println("ERROR:Failed to parse players: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 从JSON节点获取字符串值
     */
    private static String getStringValue(JsonNode node, String fieldName, String defaultValue) {
        if (node == null || !node.has(fieldName)) {
            return defaultValue;
        }
        
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return defaultValue;
        }
        
        return fieldNode.asText(defaultValue);
    }
    
    /**
     * 配置信息类
     */
    public static class ConfigInfo {
        public String templatePath;
        public String outputDir;
        public String photosDir;
        
        public ConfigInfo(String templatePath, String outputDir, String photosDir) {
            this.templatePath = templatePath;
            this.outputDir = outputDir;
            this.photosDir = photosDir;
        }
    }
    
    /**
     * 从JSON获取配置信息
     */
    public static ConfigInfo getConfigFromJson(String jsonFilePath) {
        try {
            JsonNode rootNode = objectMapper.readTree(new File(jsonFilePath));
            JsonNode configNode = rootNode.get("config");
            
            if (configNode == null) {
                System.err.println("INFO:No config found in JSON, using defaults");
                return new ConfigInfo("template.docx", "output", "photos");
            }
            
            String templatePath = getStringValue(configNode, "templatePath", "template.docx");
            String outputDir = getStringValue(configNode, "outputDir", "output");
            String photosDir = getStringValue(configNode, "photosDir", "photos");
            
            ConfigInfo config = new ConfigInfo(templatePath, outputDir, photosDir);
            System.err.println("INFO:Config parsed - template: " + templatePath + 
                             ", output: " + outputDir + ", photos: " + photosDir);
            
            return config;
            
        } catch (Exception e) {
            System.err.println("WARN:Failed to parse config, using defaults: " + e.getMessage());
            return new ConfigInfo("template.docx", "output", "photos");
        }
    }
}
