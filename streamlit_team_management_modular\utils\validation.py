#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证工具
Data Validation Utils

提供数据验证相关的工具函数
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from models.player import Player
from config.constants import FileTypes


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_team_name(team_name: str) -> <PERSON><PERSON>[bool, Optional[str]]:
        """
        验证球队名称
        
        Args:
            team_name: 球队名称
            
        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 错误消息)
        """
        if not team_name or not team_name.strip():
            return False, "球队名称不能为空"
        
        # 检查长度
        if len(team_name.strip()) > 50:
            return False, "球队名称不能超过50个字符"
        
        # 检查特殊字符
        if re.search(r'[<>:"/\\|?*]', team_name):
            return False, "球队名称不能包含特殊字符 < > : \" / \\ | ? *"
        
        return True, None
    
    @staticmethod
    def validate_player_name(name: str) -> Tuple[bool, Optional[str]]:
        """
        验证球员姓名
        
        Args:
            name: 球员姓名
            
        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 错误消息)
        """
        if not name or not name.strip():
            return False, "球员姓名不能为空"
        
        # 检查长度
        if len(name.strip()) > 20:
            return False, "球员姓名不能超过20个字符"
        
        return True, None
    
    @staticmethod
    def validate_jersey_number(jersey_number: str, existing_numbers: List[str] = None) -> Tuple[bool, Optional[str]]:
        """
        验证球衣号码
        
        Args:
            jersey_number: 球衣号码
            existing_numbers: 已存在的号码列表
            
        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 错误消息)
        """
        if not jersey_number or not str(jersey_number).strip():
            return False, "球衣号码不能为空"
        
        # 转换为字符串并去除空格
        number_str = str(jersey_number).strip()
        
        # 检查是否为数字
        if not number_str.isdigit():
            return False, "球衣号码必须是数字"
        
        # 检查范围
        number = int(number_str)
        if number < 1 or number > 99:
            return False, "球衣号码必须在1-99之间"
        
        # 检查是否重复
        if existing_numbers and number_str in existing_numbers:
            return False, f"球衣号码 {number_str} 已被使用"
        
        return True, None
    
    @staticmethod
    def validate_image_file(file) -> Tuple[bool, Optional[str]]:
        """
        验证图片文件
        
        Args:
            file: 上传的文件
            
        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 错误消息)
        """
        if file is None:
            return True, None  # 照片是可选的
        
        # 检查文件名
        if not hasattr(file, 'name') or not file.name:
            return False, "无效的文件"
        
        # 检查文件扩展名
        filename = file.name.lower()
        valid_extensions = [f'.{ext}' for ext in FileTypes.SUPPORTED_IMAGE_TYPES]
        
        if not any(filename.endswith(ext) for ext in valid_extensions):
            supported = ', '.join(FileTypes.SUPPORTED_IMAGE_TYPES).upper()
            return False, f"不支持的文件格式，请使用: {supported}"
        
        # 检查文件大小（如果可以获取）
        if hasattr(file, 'size') and file.size:
            max_size = 200 * 1024 * 1024  # 200MB
            if file.size > max_size:
                return False, "文件大小不能超过200MB"
        
        return True, None
    
    @staticmethod
    def validate_batch_player_data(players_data: List[Dict[str, Any]]) -> List[str]:
        """
        验证批量球员数据
        
        Args:
            players_data: 球员数据列表
            
        Returns:
            List[str]: 错误消息列表
        """
        errors = []
        used_numbers = []
        
        for i, data in enumerate(players_data):
            player_index = i + 1
            
            # 验证姓名
            name = data.get('name', '').strip()
            if not name:
                errors.append(f"球员 {player_index}: 姓名不能为空")
                continue
            
            # 验证球衣号码
            jersey_number = str(data.get('jersey_number', '')).strip()
            is_valid, error_msg = DataValidator.validate_jersey_number(
                jersey_number, used_numbers
            )
            
            if not is_valid:
                errors.append(f"球员 {player_index} ({name}): {error_msg}")
            else:
                used_numbers.append(jersey_number)
        
        return errors
    
    @staticmethod
    def validate_processing_config(config_data: Dict[str, Any]) -> List[str]:
        """
        验证处理配置
        
        Args:
            config_data: 配置数据
            
        Returns:
            List[str]: 错误消息列表
        """
        errors = []
        
        # 检查必要字段
        if not config_data.get('team'):
            errors.append("缺少球队信息")
        
        processing_players = config_data.get('processing_players', [])
        if not processing_players:
            errors.append("没有需要处理的球员")
        
        # 检查是否需要模板图
        needs_template = False
        for player_config in processing_players:
            process_steps = player_config.get('process_steps', {})
            if process_steps.get('fashion_tryon', False):
                needs_template = True
                break
        
        if needs_template and not config_data.get('template_image'):
            errors.append("有球员需要换装处理，但未提供模板图")
        
        return errors
