#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试背景移除和白底添加效果
Test Background Removal and White Background Addition
"""

import os
import requests
from PIL import Image

def test_background_removal_and_white_bg(image_path: str):
    """测试背景移除和白底添加"""
    
    # API配置
    api_key = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"
    base_url = "https://api.302.ai"
    
    print(f"📸 测试图片: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    # 分析原图
    analyze_image_properties(image_path, "原图")
    
    # 步骤1: 背景移除
    print("\n🎯 步骤1: 背景移除处理...")
    
    url = f"{base_url}/clipdrop/remove-background/v1"
    headers = {"x-api-key": api_key}
    
    try:
        with open(image_path, 'rb') as image_file:
            files = {
                'image_file': (os.path.basename(image_path), image_file, 'image/png')
            }
            
            print("📤 发送背景移除请求...")
            response = requests.post(url, headers=headers, files=files, timeout=30)
            
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 保存背景移除结果
            step1_output = "test_step1_no_background.png"
            with open(step1_output, 'wb') as f:
                f.write(response.content)
            print(f"✅ 背景移除完成: {step1_output}")
            
            # 分析背景移除结果
            analyze_image_properties(step1_output, "背景移除后")
            
        else:
            print(f"❌ 背景移除失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ 背景移除请求失败: {e}")
        return
    
    # 步骤2: 白底添加 - 方法1（当前实现）
    print("\n🎯 步骤2a: 白底添加（paste方法）...")
    
    try:
        subject = Image.open(step1_output).convert("RGBA")
        width, height = subject.size
        
        print(f"📏 图像尺寸: {width}x{height}")
        print(f"📊 图像模式: {subject.mode}")
        
        # 创建白色背景
        white_background = Image.new("RGB", (width, height), "white")
        
        # 当前的合成方法
        white_background.paste(subject, (0, 0), subject)
        
        step2a_output = "test_step2a_white_background_paste.png"
        white_background.save(step2a_output, "PNG")
        print(f"✅ 白底添加完成（paste方法）: {step2a_output}")
        
        # 分析结果
        analyze_image_properties(step2a_output, "白底合成后（paste）")
        
    except Exception as e:
        print(f"❌ 白底添加失败（paste方法）: {e}")
    
    # 步骤3: 白底添加 - 方法2（alpha_composite）
    print("\n🎯 步骤2b: 白底添加（alpha_composite方法）...")
    
    try:
        subject = Image.open(step1_output).convert("RGBA")
        
        # 创建白色背景
        white_bg = Image.new('RGBA', subject.size, (255, 255, 255, 255))
        
        # 使用alpha_composite方法
        result = Image.alpha_composite(white_bg, subject)
        
        step2b_output = "test_step2b_white_background_composite.png"
        result.convert('RGB').save(step2b_output, "PNG")
        print(f"✅ 白底添加完成（alpha_composite方法）: {step2b_output}")
        
        # 分析结果
        analyze_image_properties(step2b_output, "白底合成后（composite）")
        
    except Exception as e:
        print(f"❌ 白底添加失败（alpha_composite方法）: {e}")
    
    print("\n🎉 测试完成！请检查生成的文件：")
    print(f"  - 原图: {image_path}")
    print(f"  - 背景移除: test_step1_no_background.png")
    print(f"  - 白底合成1: test_step2a_white_background_paste.png")
    print(f"  - 白底合成2: test_step2b_white_background_composite.png")

def analyze_image_properties(image_path: str, description: str):
    """分析图像属性"""
    try:
        img = Image.open(image_path)
        print(f"\n📊 {description}图像分析:")
        print(f"  - 模式: {img.mode}")
        print(f"  - 尺寸: {img.size}")
        print(f"  - 格式: {img.format}")
        
        if img.mode == 'RGBA':
            # 检查透明度信息
            alpha = img.split()[-1]  # 获取alpha通道
            alpha_values = list(alpha.getdata())
            unique_alpha = set(alpha_values)
            print(f"  - Alpha通道值范围: {min(unique_alpha)} - {max(unique_alpha)}")
            print(f"  - 是否有透明像素: {'是' if 0 in unique_alpha else '否'}")
            
            # 统计透明像素比例
            transparent_count = alpha_values.count(0)
            total_pixels = len(alpha_values)
            transparent_ratio = transparent_count / total_pixels * 100
            print(f"  - 透明像素比例: {transparent_ratio:.2f}%")
        
    except Exception as e:
        print(f"❌ 图像分析失败: {e}")

if __name__ == "__main__":
    # 测试图片路径
    test_image = r"streamlit_team_management_modular\data\user_a10693c387d7\templates\team_templates\11122\微信图片_2025-08-19_095124_909_20250823_162558.jpg"
    
    print("🧪 开始背景移除和白底添加测试")
    print("=" * 50)
    
    test_background_removal_and_white_bg(test_image)
