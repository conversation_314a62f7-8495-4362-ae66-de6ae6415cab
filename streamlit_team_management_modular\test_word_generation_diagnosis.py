#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word生成问题诊断测试脚本
Diagnosis Test Script for Word Generation Issues
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_user_data_structure():
    """测试用户数据结构"""
    print("🔍 测试1: 检查用户数据结构")
    
    user_id = "37d53472d725"
    team_name = "003222"
    user_data_path = f"data/user_{user_id}"
    
    print(f"用户ID: {user_id}")
    print(f"球队名称: {team_name}")
    print(f"用户数据路径: {user_data_path}")
    
    # 检查关键文件
    files_to_check = [
        f"{user_data_path}/teams/{team_name}.json",
        f"{user_data_path}/enhanced_ai_data/{team_name}_ai_data.json",
        f"{user_data_path}/exports/team_{team_name}_ai_ready.json",
        f"{user_data_path}/fashion_workflow/workflow_{team_name}_20250824_141624.json"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} - 存在")
        else:
            print(f"❌ {file_path} - 不存在")
    
    return user_id, team_name

def test_ai_export_data_loading(user_id, team_name):
    """测试AI导出数据加载"""
    print("\n🔍 测试2: AI导出数据加载")
    
    # 模拟session state
    import streamlit as st
    if 'user_id' not in st.session_state:
        st.session_state.user_id = user_id
    
    try:
        from services.fashion_workflow_service import FashionWorkflowService
        from services.auth_service import AuthService
        
        auth_service = AuthService()
        workflow_service = FashionWorkflowService(user_id)
        
        # 测试AI导出数据加载
        ai_export_data = workflow_service._load_ai_export_data(team_name)
        
        if ai_export_data:
            print("✅ AI导出数据加载成功")
            print(f"   球队信息: {ai_export_data.get('team_info', {}).get('name', 'N/A')}")
            print(f"   球员数量: {len(ai_export_data.get('players', []))}")
            
            # 检查球员照片信息
            players_with_photos = 0
            for player in ai_export_data.get('players', []):
                photo_info = player.get('photo_info', {})
                if photo_info.get('exists', False) and photo_info.get('absolute_path'):
                    players_with_photos += 1
                    print(f"   球员 {player.get('name', 'N/A')}: 有照片 ✅")
                else:
                    print(f"   球员 {player.get('name', 'N/A')}: 无照片 ❌")
            
            print(f"   有照片的球员: {players_with_photos}")
            return ai_export_data, workflow_service
        else:
            print("❌ AI导出数据加载失败")
            return None, workflow_service
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None, None

def test_fashion_readiness(workflow_service, team_name):
    """测试换装准备状态检查"""
    print("\n🔍 测试3: 换装准备状态检查")
    
    if not workflow_service:
        print("❌ 工作流服务不可用")
        return None
    
    try:
        readiness = workflow_service.check_fashion_readiness(team_name)
        
        print(f"准备状态: {'✅ 就绪' if readiness.get('ready', False) else '❌ 未就绪'}")
        print(f"原因: {readiness.get('reason', 'N/A')}")
        print(f"消息: {readiness.get('message', 'N/A')}")
        print(f"总球员数: {readiness.get('total_players', 0)}")
        print(f"有照片球员数: {readiness.get('ready_players', 0)}")
        
        return readiness
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def test_photo_path_resolution(user_id, team_name):
    """测试照片路径解析"""
    print("\n🔍 测试4: 照片路径解析")
    
    try:
        # 检查球员数据
        team_file = f"data/user_{user_id}/teams/{team_name}.json"
        if os.path.exists(team_file):
            with open(team_file, 'r', encoding='utf-8') as f:
                team_data = json.load(f)
            
            for player in team_data.get('players', []):
                player_name = player.get('name', 'N/A')
                photo_filename = player.get('photo', '')
                
                if photo_filename:
                    # 检查不同的照片路径
                    photo_paths = [
                        f"data/user_{user_id}/photos/{photo_filename}",
                        f"data/user_{user_id}/photos/{team_name}/{photo_filename}",
                        f"data/user_{user_id}/processed_photos/{team_name}/{player.get('id', '')}_fashion_final.png"
                    ]
                    
                    print(f"球员 {player_name}:")
                    for path in photo_paths:
                        if os.path.exists(path):
                            print(f"   ✅ {path}")
                        else:
                            print(f"   ❌ {path}")
                else:
                    print(f"球员 {player_name}: 无照片文件名")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_word_generation_prerequisites():
    """测试Word生成的前置条件"""
    print("\n🔍 测试5: Word生成前置条件")
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 检查Word生成器配置
        global_paths = app_settings.word_generator.get_absolute_paths()
        
        print("Word生成器配置:")
        print(f"   JAR路径: {global_paths.get('jar_path', 'N/A')}")
        print(f"   模板路径: {global_paths.get('template_path', 'N/A')}")
        
        # 检查文件是否存在
        jar_exists = os.path.exists(global_paths.get('jar_path', ''))
        template_exists = os.path.exists(global_paths.get('template_path', ''))
        
        print(f"   JAR文件存在: {'✅' if jar_exists else '❌'}")
        print(f"   模板文件存在: {'✅' if template_exists else '❌'}")
        
        return jar_exists and template_exists
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Word生成问题诊断测试")
    print("=" * 50)
    
    # 测试1: 用户数据结构
    user_id, team_name = test_user_data_structure()
    
    # 测试2: AI导出数据加载
    ai_export_data, workflow_service = test_ai_export_data_loading(user_id, team_name)
    
    # 测试3: 换装准备状态检查
    readiness = test_fashion_readiness(workflow_service, team_name)
    
    # 测试4: 照片路径解析
    test_photo_path_resolution(user_id, team_name)
    
    # 测试5: Word生成前置条件
    word_ready = test_word_generation_prerequisites()
    
    # 总结
    print("\n📊 诊断总结")
    print("=" * 50)
    
    if readiness:
        if readiness.get('ready', False):
            print("✅ 换装准备状态: 就绪")
            print("   → 应该会自动生成Word")
        else:
            print("❌ 换装准备状态: 未就绪")
            print(f"   → 原因: {readiness.get('reason', 'N/A')}")
            print("   → 这就是为什么没有自动生成Word的原因!")
    
    if word_ready:
        print("✅ Word生成器: 配置正确")
    else:
        print("❌ Word生成器: 配置有问题")
    
    print("\n🎯 建议的解决方案:")
    if readiness and not readiness.get('ready', False):
        reason = readiness.get('reason', '')
        if reason == 'no_ai_data':
            print("1. 检查AI数据文件是否正确生成")
            print("2. 确保AI聊天数据转换逻辑正常工作")
        elif reason == 'no_photos':
            print("1. 检查照片路径解析逻辑")
            print("2. 确保照片文件存在且路径正确")
        else:
            print(f"1. 解决具体问题: {readiness.get('message', 'N/A')}")

if __name__ == "__main__":
    main()
