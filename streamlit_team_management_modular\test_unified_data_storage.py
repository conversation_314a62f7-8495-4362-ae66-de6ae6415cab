#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据存储测试
Test Unified Data Storage

测试所有用户数据是否正确保存到 data/user_xxx/ 目录下
"""

import os
import sys
import json
import tempfile
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import app_settings
from data.file_manager import FileManager
from services.export_service import ExportService
from services.auth_service import AuthService
from models.team import Team
from models.player import Player


class UnifiedDataStorageTest:
    """统一数据存储测试类"""
    
    def __init__(self):
        self.test_user_id = "test_unified_storage"
        self.test_team_name = "测试球队_统一存储"
        self.test_results = []
        self.data_dir = Path("data")
        self.user_data_dir = self.data_dir / self.test_user_id
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        result = {
            "timestamp": timestamp,
            "test_name": test_name,
            "success": success,
            "details": details
        }
        
        self.test_results.append(result)
        print(f"[{timestamp}] {status} {test_name}")
        if details:
            print(f"    📝 {details}")
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 清理可能存在的测试用户数据
        if self.user_data_dir.exists():
            shutil.rmtree(self.user_data_dir)
        
        # 创建测试用户目录结构
        auth_service = AuthService()
        auth_service.create_user_data_folder(self.test_user_id)
        
        self.log_test("测试环境设置", True, f"创建用户目录: {self.user_data_dir}")
    
    def test_file_manager_paths(self):
        """测试FileManager的路径配置"""
        print("\n📁 测试FileManager路径配置...")
        
        file_manager = FileManager(self.test_user_id)
        
        # 检查各个路径是否指向正确的用户目录
        expected_paths = {
            "upload_folder": self.user_data_dir / "photos",
            "processed_folder": self.user_data_dir / "processed_photos",
            "temp_fashion_folder": self.user_data_dir / "temp_fashion",
            "word_output_folder": self.user_data_dir / "word_output",
            "templates_folder": self.user_data_dir / "templates",
            "logos_folder": self.user_data_dir / "logos"
        }
        
        for attr_name, expected_path in expected_paths.items():
            actual_path = Path(getattr(file_manager, attr_name))
            success = actual_path == expected_path
            
            self.log_test(
                f"FileManager.{attr_name}",
                success,
                f"期望: {expected_path}, 实际: {actual_path}"
            )
    
    def test_export_service_paths(self):
        """测试ExportService的路径配置"""
        print("\n📤 测试ExportService路径配置...")
        
        export_service = ExportService(self.test_user_id)
        
        expected_exports_path = self.user_data_dir / "exports"
        actual_exports_path = Path(export_service.exports_folder)
        
        success = actual_exports_path == expected_exports_path
        self.log_test(
            "ExportService.exports_folder",
            success,
            f"期望: {expected_exports_path}, 实际: {actual_exports_path}"
        )
    
    def test_word_generator_paths(self):
        """测试WordGenerator的路径配置"""
        print("\n📄 测试WordGenerator路径配置...")
        
        paths = app_settings.word_generator.get_absolute_paths(self.test_user_id)
        
        expected_output_dir = self.user_data_dir / "word_output"
        actual_output_dir = Path(paths['output_dir'])
        
        success = actual_output_dir.resolve() == expected_output_dir.resolve()
        self.log_test(
            "WordGenerator.output_dir",
            success,
            f"期望: {expected_output_dir}, 实际: {actual_output_dir}"
        )
    
    def test_actual_file_operations(self):
        """测试实际的文件操作"""
        print("\n💾 测试实际文件操作...")
        
        file_manager = FileManager(self.test_user_id)
        
        # 1. 测试照片保存
        test_photo_content = b"fake_photo_content"
        test_photo_name = "test_player.jpg"
        
        team_upload_folder = file_manager.get_team_upload_folder(self.test_team_name)
        test_photo_path = os.path.join(team_upload_folder, test_photo_name)
        
        try:
            with open(test_photo_path, 'wb') as f:
                f.write(test_photo_content)
            
            # 验证文件是否保存在正确位置
            expected_photo_path = self.user_data_dir / "photos" / self.test_team_name / test_photo_name
            success = Path(test_photo_path) == expected_photo_path and os.path.exists(test_photo_path)
            
            self.log_test(
                "照片文件保存",
                success,
                f"保存位置: {test_photo_path}"
            )
            
        except Exception as e:
            self.log_test("照片文件保存", False, f"错误: {e}")
        
        # 2. 测试处理后照片保存
        processed_folder = file_manager.get_team_processed_folder(self.test_team_name)
        test_processed_path = os.path.join(processed_folder, "processed_" + test_photo_name)
        
        try:
            with open(test_processed_path, 'wb') as f:
                f.write(test_photo_content)
            
            expected_processed_path = self.user_data_dir / "processed_photos" / self.test_team_name / ("processed_" + test_photo_name)
            success = Path(test_processed_path) == expected_processed_path and os.path.exists(test_processed_path)
            
            self.log_test(
                "处理后照片保存",
                success,
                f"保存位置: {test_processed_path}"
            )
            
        except Exception as e:
            self.log_test("处理后照片保存", False, f"错误: {e}")
        
        # 3. 测试换装临时文件保存
        temp_fashion_folder = file_manager.get_team_temp_fashion_folder(self.test_team_name)
        test_fashion_path = os.path.join(temp_fashion_folder, "fashion_result.png")
        
        try:
            with open(test_fashion_path, 'wb') as f:
                f.write(test_photo_content)
            
            expected_fashion_path = self.user_data_dir / "temp_fashion" / self.test_team_name / "fashion_result.png"
            success = Path(test_fashion_path) == expected_fashion_path and os.path.exists(test_fashion_path)
            
            self.log_test(
                "换装临时文件保存",
                success,
                f"保存位置: {test_fashion_path}"
            )
            
        except Exception as e:
            self.log_test("换装临时文件保存", False, f"错误: {e}")
    
    def test_export_service_operations(self):
        """测试ExportService的实际操作"""
        print("\n📊 测试ExportService实际操作...")
        
        export_service = ExportService(self.test_user_id)
        
        # 创建测试数据
        test_export_data = {
            "team_info": {
                "name": self.test_team_name,
                "display_name": self.test_team_name,
                "total_players": 1
            },
            "players": [
                {
                    "id": "test_player_1",
                    "name": "测试球员1",
                    "position": "前锋"
                }
            ],
            "export_time": datetime.now().isoformat(),
            "test_flag": True
        }
        
        try:
            # 模拟导出操作
            safe_team_name = self.test_team_name.replace(" ", "_")
            export_filename = f"team_{safe_team_name}_ai_ready.json"
            export_path = os.path.join(export_service.exports_folder, export_filename)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(test_export_data, f, ensure_ascii=False, indent=2)
            
            # 验证文件是否保存在正确位置
            expected_export_path = self.user_data_dir / "exports" / export_filename
            success = Path(export_path) == expected_export_path and os.path.exists(export_path)
            
            self.log_test(
                "AI导出数据保存",
                success,
                f"保存位置: {export_path}"
            )
            
        except Exception as e:
            self.log_test("AI导出数据保存", False, f"错误: {e}")
    
    def test_no_global_data_leakage(self):
        """测试是否有数据泄露到全局目录"""
        print("\n🔒 测试全局目录数据泄露...")
        
        # 检查旧的全局目录是否还在被使用
        old_global_dirs = [
            "uploads",
            "processed_photos", 
            "ai_export",
            "word_output",
            "temp_files"
        ]
        
        for dir_name in old_global_dirs:
            dir_path = Path(dir_name)
            
            if dir_path.exists():
                # 检查是否有新的测试数据
                has_new_test_data = False
                try:
                    for item in dir_path.rglob("*"):
                        if item.is_file():
                            # 检查文件修改时间是否是最近的（测试期间）
                            mtime = datetime.fromtimestamp(item.stat().st_mtime)
                            if (datetime.now() - mtime).seconds < 300:  # 5分钟内
                                has_new_test_data = True
                                break
                except:
                    pass
                
                success = not has_new_test_data
                self.log_test(
                    f"全局目录 {dir_name} 无新数据",
                    success,
                    "检查是否有测试期间的新文件" if success else "发现测试期间的新文件"
                )
            else:
                self.log_test(
                    f"全局目录 {dir_name} 已清理",
                    True,
                    "目录不存在（已被清理）"
                )
    
    def test_user_data_completeness(self):
        """测试用户数据目录完整性"""
        print("\n📋 测试用户数据目录完整性...")
        
        expected_subdirs = [
            "teams", "photos", "templates", "processed_photos", 
            "word_output", "temp_fashion", "logos", 
            "enhanced_ai_data", "fashion_workflow", "exports"
        ]
        
        for subdir in expected_subdirs:
            subdir_path = self.user_data_dir / subdir
            success = subdir_path.exists() and subdir_path.is_dir()
            
            self.log_test(
                f"用户子目录 {subdir}",
                success,
                f"路径: {subdir_path}"
            )
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        try:
            if self.user_data_dir.exists():
                shutil.rmtree(self.user_data_dir)
            
            self.log_test("测试环境清理", True, f"删除测试用户目录: {self.user_data_dir}")
            
        except Exception as e:
            self.log_test("测试环境清理", False, f"错误: {e}")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 统一数据存储测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['details']}")
        
        # 保存详细报告
        report_file = self.data_dir / "unified_storage_test_report.json"
        report_data = {
            "test_time": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": passed_tests/total_tests*100
            },
            "test_results": self.test_results
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 详细报告已保存到: {report_file}")
        
        return failed_tests == 0
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始统一数据存储测试")
        print("="*60)
        
        try:
            # 设置测试环境
            self.setup_test_environment()
            
            # 运行各项测试
            self.test_file_manager_paths()
            self.test_export_service_paths()
            self.test_word_generator_paths()
            self.test_actual_file_operations()
            self.test_export_service_operations()
            self.test_no_global_data_leakage()
            self.test_user_data_completeness()
            
            # 生成测试报告
            success = self.generate_test_report()
            
            return success
            
        finally:
            # 清理测试环境
            self.cleanup_test_environment()


def main():
    """主函数"""
    print("🎯 统一数据存储测试工具")
    print("测试所有用户数据是否正确保存到 data/user_xxx/ 目录下")
    print()
    
    # 运行测试
    test_runner = UnifiedDataStorageTest()
    success = test_runner.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！用户数据已正确统一到 data/user_xxx/ 目录")
    else:
        print("\n⚠️ 部分测试失败，请检查配置和实现")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
