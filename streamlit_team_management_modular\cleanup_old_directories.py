#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旧目录清理脚本
Old Directory Cleanup Script

清理迁移后的旧分散目录，简化项目结构
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any


class DirectoryCleanupTool:
    """目录清理工具"""
    
    def __init__(self):
        self.base_dir = Path(".")
        self.data_dir = self.base_dir / "data"
        self.cleanup_log = []
        
        # 需要清理的旧目录
        self.directories_to_cleanup = [
            "uploads",
            "processed_photos", 
            "ai_export",
            "word_output",
            "temp_files",
            # 测试和示例目录
            "demo",
            "real_demo",
            "test_correct",
            "test_images", 
            "test_simple",
            "visual_example",
            # 其他分散目录
            "output",
            "cache"
        ]
        
        # 需要保留的重要目录
        self.keep_directories = [
            "data",
            "components",
            "services", 
            "config",
            "models",
            "utils",
            "assets",  # 如果存在
            "temp"     # 系统级临时目录
        ]
    
    def log_action(self, action: str, details: str = ""):
        """记录清理操作"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {action}"
        if details:
            log_entry += f" - {details}"
        
        self.cleanup_log.append(log_entry)
        print(log_entry)
    
    def check_migration_status(self) -> bool:
        """检查数据迁移是否完成"""
        backup_info_file = self.data_dir / "backup_info.json"
        
        if not backup_info_file.exists():
            self.log_action("警告", "未找到迁移备份信息文件，请先运行数据迁移")
            return False
        
        try:
            with open(backup_info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
            
            if backup_info.get("migration_status") == "completed":
                self.log_action("确认", "数据迁移已完成，可以安全清理旧目录")
                return True
            else:
                self.log_action("警告", "数据迁移状态不明确，建议检查后再清理")
                return False
                
        except Exception as e:
            self.log_action("错误", f"读取备份信息失败: {e}")
            return False
    
    def analyze_directory_usage(self, dir_path: Path) -> Dict[str, Any]:
        """分析目录使用情况"""
        if not dir_path.exists():
            return {"exists": False}
        
        analysis = {
            "exists": True,
            "is_empty": True,
            "file_count": 0,
            "total_size": 0,
            "subdirs": []
        }
        
        try:
            for item in dir_path.rglob("*"):
                if item.is_file():
                    analysis["file_count"] += 1
                    analysis["total_size"] += item.stat().st_size
                    analysis["is_empty"] = False
                elif item.is_dir() and item.parent == dir_path:
                    analysis["subdirs"].append(item.name)
        
        except Exception as e:
            analysis["error"] = str(e)
        
        return analysis
    
    def create_backup_before_cleanup(self):
        """清理前创建备份记录"""
        backup_record = {
            "cleanup_time": datetime.now().isoformat(),
            "directories_analyzed": {},
            "cleanup_plan": self.directories_to_cleanup
        }
        
        # 分析每个要清理的目录
        for dir_name in self.directories_to_cleanup:
            dir_path = self.base_dir / dir_name
            analysis = self.analyze_directory_usage(dir_path)
            backup_record["directories_analyzed"][dir_name] = analysis
            
            if analysis["exists"]:
                size_mb = analysis["total_size"] / (1024 * 1024)
                self.log_action(
                    f"分析目录 {dir_name}", 
                    f"文件数: {analysis['file_count']}, 大小: {size_mb:.2f}MB"
                )
        
        # 保存备份记录
        backup_file = self.data_dir / "cleanup_backup_record.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_record, f, ensure_ascii=False, indent=2)
        
        self.log_action("创建清理备份记录", str(backup_file))
    
    def cleanup_directory(self, dir_name: str) -> bool:
        """清理指定目录"""
        dir_path = self.base_dir / dir_name
        
        if not dir_path.exists():
            self.log_action(f"跳过不存在的目录", dir_name)
            return True
        
        try:
            # 检查是否为空目录
            analysis = self.analyze_directory_usage(dir_path)
            
            if analysis["is_empty"]:
                # 空目录直接删除
                dir_path.rmdir()
                self.log_action(f"删除空目录", dir_name)
            else:
                # 非空目录移动到备份位置
                backup_dir = self.data_dir / "old_directories_backup"
                backup_dir.mkdir(exist_ok=True)
                
                backup_path = backup_dir / dir_name
                shutil.move(str(dir_path), str(backup_path))
                self.log_action(
                    f"移动目录到备份", 
                    f"{dir_name} -> old_directories_backup/{dir_name}"
                )
            
            return True
            
        except Exception as e:
            self.log_action(f"清理目录失败", f"{dir_name}: {e}")
            return False
    
    def run_cleanup(self, force: bool = False):
        """执行目录清理"""
        self.log_action("开始目录清理", "清理旧的分散目录结构")
        
        # 1. 检查迁移状态
        if not force and not self.check_migration_status():
            self.log_action("清理终止", "数据迁移未完成或状态不明")
            return False
        
        # 2. 创建清理前备份记录
        self.create_backup_before_cleanup()
        
        # 3. 清理各个目录
        success_count = 0
        for dir_name in self.directories_to_cleanup:
            if self.cleanup_directory(dir_name):
                success_count += 1
        
        # 4. 保存清理日志
        self.save_cleanup_log()
        
        self.log_action(
            "目录清理完成", 
            f"成功清理 {success_count}/{len(self.directories_to_cleanup)} 个目录"
        )
        
        return success_count == len(self.directories_to_cleanup)
    
    def save_cleanup_log(self):
        """保存清理日志"""
        log_file = self.data_dir / "cleanup_log.txt"
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("目录清理日志\n")
            f.write("=" * 50 + "\n")
            f.write(f"清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"清理项目: 移除旧的分散目录结构\n\n")
            
            for log_entry in self.cleanup_log:
                f.write(log_entry + "\n")
        
        self.log_action(f"保存清理日志", str(log_file))
    
    def show_final_structure(self):
        """显示清理后的目录结构"""
        self.log_action("清理后的项目结构", "")
        
        important_dirs = []
        for item in self.base_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                important_dirs.append(item.name)
        
        important_dirs.sort()
        for dir_name in important_dirs:
            print(f"  📁 {dir_name}/")
        
        print("\n✨ 项目结构已简化，所有用户数据统一在 data/ 目录下")


def main():
    """主函数"""
    print("🧹 目录清理工具 - 清理旧的分散目录")
    print("=" * 50)
    
    cleanup_tool = DirectoryCleanupTool()
    
    # 显示将要清理的目录
    print("将要清理的目录:")
    for dir_name in cleanup_tool.directories_to_cleanup:
        dir_path = Path(dir_name)
        status = "✅ 存在" if dir_path.exists() else "❌ 不存在"
        print(f"  📁 {dir_name} - {status}")
    
    print("\n保留的重要目录:")
    for dir_name in cleanup_tool.keep_directories:
        print(f"  📁 {dir_name}")
    
    # 确认清理
    print("\n⚠️  注意：此操作将移除或备份旧的目录结构")
    print("💡 非空目录将移动到 data/old_directories_backup/")
    print("💡 空目录将直接删除")
    
    response = input("\n是否继续清理？(y/N): ")
    if response.lower() != 'y':
        print("❌ 清理已取消")
        return
    
    # 执行清理
    success = cleanup_tool.run_cleanup()
    
    if success:
        print("\n✅ 目录清理完成！")
        cleanup_tool.show_final_structure()
        print("\n📋 清理日志已保存到 data/cleanup_log.txt")
        print("📦 备份记录已保存到 data/cleanup_backup_record.json")
    else:
        print("\n⚠️ 清理过程中遇到问题，请检查日志")


if __name__ == "__main__":
    main()
