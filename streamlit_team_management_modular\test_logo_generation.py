#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试队徽生成功能
Test Logo Generation

测试AI自动生成队徽功能是否正常工作
"""

import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_ai_image_generation_service():
    """测试AI图片生成服务"""
    print("🧪 测试AI图片生成服务")
    print("=" * 50)
    
    try:
        from services.ai_image_generation_service import AIImageGenerationService
        
        # 测试服务初始化
        test_user_id = "test_user_logo"
        image_service = AIImageGenerationService(test_user_id)
        
        # 检查API密钥
        if image_service.api_key:
            print("✅ API密钥已配置")
        else:
            print("❌ API密钥未配置")
            return False
        
        # 检查用户文件夹
        if os.path.exists(image_service.logos_folder):
            print(f"✅ 队徽文件夹已创建: {image_service.logos_folder}")
        else:
            print(f"❌ 队徽文件夹不存在: {image_service.logos_folder}")
            return False
        
        print("✅ AI图片生成服务测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_ai_service_integration():
    """测试增强AI服务集成"""
    print("\n🧪 测试增强AI服务集成")
    print("=" * 50)
    
    try:
        from services.enhanced_ai_service import enhanced_ai_assistant
        
        # 检查服务可用性
        if enhanced_ai_assistant.is_available():
            print("✅ 增强AI服务可用")
        else:
            print("❌ 增强AI服务不可用")
            return False
        
        # 检查队徽生成方法
        if hasattr(enhanced_ai_assistant, '_generate_team_logo'):
            print("✅ 队徽生成方法存在")
        else:
            print("❌ 队徽生成方法不存在")
            return False
        
        print("✅ 增强AI服务集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fashion_workflow_integration():
    """测试换装工作流集成"""
    print("\n🧪 测试换装工作流集成")
    print("=" * 50)
    
    try:
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 测试服务初始化
        test_user_id = "test_user_workflow_logo"
        workflow_service = FashionWorkflowService(test_user_id)
        
        # 检查自动生成队徽方法
        if hasattr(workflow_service, '_auto_generate_team_logo'):
            print("✅ 自动生成队徽方法存在")
        else:
            print("❌ 自动生成队徽方法不存在")
            return False
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(workflow_service._auto_generate_team_logo)
        params = list(sig.parameters.keys())
        
        if 'team_name' in params:
            print("✅ 队徽生成方法参数正确")
        else:
            print(f"❌ 队徽生成方法参数错误: {params}")
            return False
        
        print("✅ 换装工作流集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_service_logos_folder():
    """测试AuthService队徽文件夹创建"""
    print("\n🧪 测试AuthService队徽文件夹创建")
    print("=" * 50)
    
    try:
        from services.auth_service import AuthService
        
        auth_service = AuthService()
        test_user_id = "test_user_auth_logo"
        
        # 创建用户文件夹
        user_folder = auth_service.create_user_data_folder(test_user_id)
        print(f"✅ 用户文件夹创建: {user_folder}")
        
        # 检查logos文件夹
        logos_folder = os.path.join(user_folder, 'logos')
        if os.path.exists(logos_folder):
            print(f"✅ logos文件夹存在: {logos_folder}")
        else:
            print(f"❌ logos文件夹不存在: {logos_folder}")
            return False
        
        print("✅ AuthService队徽文件夹创建测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_word_generation_integration():
    """测试Word生成集成"""
    print("\n🧪 测试Word生成集成")
    print("=" * 50)
    
    try:
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 测试服务初始化
        test_user_id = "test_user_word_logo"
        workflow_service = FashionWorkflowService(test_user_id)
        
        # 检查Word生成方法签名
        import inspect
        sig = inspect.signature(workflow_service._auto_generate_word_document)
        params = list(sig.parameters.keys())
        
        expected_params = ['team_name', 'player_photo_mapping', 'logo_path']
        if all(param in params for param in expected_params):
            print("✅ Word生成方法签名正确")
        else:
            print(f"❌ Word生成方法签名错误: {params}")
            return False
        
        print("✅ Word生成集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_configuration():
    """测试API配置"""
    print("\n🧪 测试API配置")
    print("=" * 50)
    
    try:
        from services.ai_image_generation_service import AIImageGenerationService
        
        # 测试API配置
        image_service = AIImageGenerationService()
        
        # 检查API密钥
        if image_service.api_key == "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o":
            print("✅ API密钥配置正确")
        else:
            print("❌ API密钥配置错误")
            return False
        
        # 检查API地址
        if image_service.base_url == "https://api.302.ai":
            print("✅ API地址配置正确")
        else:
            print("❌ API地址配置错误")
            return False
        
        print("✅ API配置测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_workflow():
    """测试完整工作流"""
    print("\n🧪 测试完整工作流")
    print("=" * 50)
    
    try:
        # 模拟完整的队徽生成工作流
        test_user_id = "test_user_complete"
        test_team_name = "测试足球队"
        
        # 1. 创建用户文件夹
        from services.auth_service import AuthService
        auth_service = AuthService()
        user_folder = auth_service.create_user_data_folder(test_user_id)
        print(f"✅ 步骤1: 用户文件夹创建成功")
        
        # 2. 检查队徽文件夹
        logos_folder = os.path.join(user_folder, 'logos')
        if os.path.exists(logos_folder):
            print(f"✅ 步骤2: 队徽文件夹存在")
        else:
            print(f"❌ 步骤2: 队徽文件夹不存在")
            return False
        
        # 3. 初始化图片生成服务
        from services.ai_image_generation_service import AIImageGenerationService
        image_service = AIImageGenerationService(test_user_id)
        print(f"✅ 步骤3: 图片生成服务初始化成功")
        
        # 4. 初始化工作流服务
        from services.fashion_workflow_service import FashionWorkflowService
        workflow_service = FashionWorkflowService(test_user_id)
        print(f"✅ 步骤4: 工作流服务初始化成功")
        
        # 5. 检查队徽生成方法可调用性
        if hasattr(workflow_service, '_auto_generate_team_logo'):
            print(f"✅ 步骤5: 队徽生成方法可调用")
        else:
            print(f"❌ 步骤5: 队徽生成方法不可调用")
            return False
        
        print("✅ 完整工作流测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始队徽生成功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行测试
    test_results.append(("API配置", test_api_configuration()))
    test_results.append(("AuthService队徽文件夹", test_auth_service_logos_folder()))
    test_results.append(("AI图片生成服务", test_ai_image_generation_service()))
    test_results.append(("增强AI服务集成", test_enhanced_ai_service_integration()))
    test_results.append(("换装工作流集成", test_fashion_workflow_integration()))
    test_results.append(("Word生成集成", test_word_generation_integration()))
    test_results.append(("完整工作流", test_complete_workflow()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！队徽生成功能已就绪。")
        print("\n📋 功能说明:")
        print("- ✅ 在AI换装时自动生成队徽")
        print("- ✅ 队徽保存到用户专属文件夹")
        print("- ✅ 队徽自动插入Word报名表")
        print("- ✅ 支持302.ai的Midjourney API")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
