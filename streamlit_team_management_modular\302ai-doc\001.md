# 图片生成（OpenAI格式）

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /302/images/generations:
    post:
      summary: 图片生成（OpenAI格式）
      deprecated: false
      description: |-
        模型列表&&传参说明：
        https://kdocs.cn/l/cfKj44pbDjO2

        openai SDK调用示例
        ```python
        import base64
        from openai import OpenAI

        api_key = ""
        # 指定 base_url
        client = OpenAI(base_url="https://api.302.ai/302", api_key=api_key)

        img = client.images.generate(
            model="hidream-i1-fast",
            prompt="A cute baby sea otter",
            n=1,
            size="728x728",
            response_format="b64_json",
            extra_body={"aspect_ratio": "16:9"},
        )
        image_bytes = base64.b64decode(img.data[0].b64_json)
        with open("output.png", "wb") as f:
            f.write(image_bytes)

        ```
      tags:
        - 图片生成/统一接口/OpenAI格式
      parameters:
        - name: webhook
          in: query
          description: 回传链接， post方法
          required: false
          schema:
            type: string
        - name: Authorization
          in: header
          description: ''
          required: true
          example: Bearer {{YOUR_API_KEY}}
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                model:
                  type: string
                  title: 模型
                prompt:
                  type: string
                  title: 提示词
                background:
                  type: string
                  default: auto
                  title: 背景
                moderation:
                  type: string
                  title: 内容审核
                'n':
                  type: integer
                  default: 1
                  title: 生成的图片数量
                output_compression:
                  type: integer
                  default: 100
                  title: 输出压缩
                output_format:
                  type: string
                  title: 图片格式
                  enum:
                    - png
                    - jpeg
                    - webp
                  x-apifox-enum:
                    - value: png
                      name: ''
                      description: ''
                    - value: jpeg
                      name: ''
                      description: ''
                    - value: webp
                      name: ''
                      description: ''
                quality:
                  type: string
                  title: 生成质量
                response_format:
                  type: string
                  title: 响应格式
                  enum:
                    - url
                    - bs64_json
                    - 其他任何值
                  x-apifox-enum:
                    - value: url
                      name: ''
                      description: ''
                    - value: bs64_json
                      name: ''
                      description: ''
                    - value: 其他任何值
                      name: ''
                      description: url, bs64都会返回
                size:
                  type: string
                  title: 图片尺寸
                style:
                  type: string
                  title: 风格
                user:
                  type: string
                  title: 用户标志
                更多非openai供应商模型的额外字段见文档说明:
                  type: string
              x-apifox-orders:
                - model
                - prompt
                - background
                - moderation
                - 'n'
                - output_compression
                - output_format
                - quality
                - response_format
                - size
                - style
                - user
                - 更多非openai供应商模型的额外字段见文档说明
              required:
                - model
                - prompt
            example:
              prompt: A cute baby sea otter
              model: hidream-i1-fast
              'n': 1
              response_format: url
              size: 728x728
              aspect_ratio: '16:9'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  created:
                    type: integer
                    title: 生成的时间戳
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        url:
                          type: string
                        bs62_json:
                          type: string
                      x-apifox-orders:
                        - url
                        - bs62_json
                  usage:
                    type: object
                    properties:
                      total_tokens:
                        type: integer
                      input_tokens:
                        type: integer
                      output_tokens:
                        type: integer
                      input_tokens_details:
                        type: object
                        properties:
                          text_tokens:
                            type: integer
                          image_tokens:
                            type: integer
                        required:
                          - text_tokens
                          - image_tokens
                        x-apifox-orders:
                          - text_tokens
                          - image_tokens
                    required:
                      - total_tokens
                      - input_tokens
                      - output_tokens
                      - input_tokens_details
                    x-apifox-orders:
                      - total_tokens
                      - input_tokens
                      - output_tokens
                      - input_tokens_details
                    title: token详情
                    description: 除了gpt-image-1模型外，其他供应商的模型是按次数收费，所以token的值都是0
                required:
                  - created
                  - data
                  - usage
                x-apifox-orders:
                  - created
                  - data
                  - usage
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 图片生成/统一接口/OpenAI格式
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/4012774/apis/api-323501848-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: https://api.302.ai
    description: 正式环境
  - url: https://api.302ai.cn
    description: 国内中转
security: []

```

import base64
from openai import OpenAI

api_key = ""
# 指定 base_url
client = OpenAI(base_url="https://api.302.ai/302", api_key=api_key)

img = client.images.generate(
    model="hidream-i1-fast",
    prompt="A cute baby sea otter",
    n=1,
    size="728x728",
    response_format="b64_json",
    extra_body={"aspect_ratio": "16:9"},
)
image_bytes = base64.b64decode(img.data[0].b64_json)
with open("output.png", "wb") as f:
    f.write(image_bytes)



    import http.client
import json

conn = http.client.HTTPSConnection("api.302.ai")
payload = json.dumps({
   "prompt": "A cute baby sea otter",
   "model": "hidream-i1-fast",
   "n": 1,
   "response_format": "url",
   "size": "728x728",
   "aspect_ratio": "16:9"
})
headers = {
   'Authorization': 'Bearer ',
   'Content-Type': 'application/json'
}
conn.request("POST", "/302/images/generations?webhook=null", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))


import requests
import json

url = "https://api.302.ai/302/images/generations?webhook"

payload = json.dumps({
   "prompt": "A cute baby sea otter",
   "model": "hidream-i1-fast",
   "n": 1,
   "response_format": "url",
   "size": "728x728",
   "aspect_ratio": "16:9"
})
headers = {
   'Authorization': 'Bearer ',
   'Content-Type': 'application/json'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)


