#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试双API配置
Test Dual API Configuration

验证OpenAI官方API和302.ai API的配置是否正确
"""

import os
import sys
import requests
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import app_settings


def test_openai_official_api():
    """测试OpenAI官方API"""
    print("🤖 测试OpenAI官方API...")
    print("=" * 50)
    
    try:
        from openai import OpenAI
        
        # 获取API Key
        api_key = app_settings.ai.get_openai_api_key()
        if not api_key:
            print("❌ 未配置OpenAI官方API Key")
            return False
        
        print(f"🔑 API Key: {api_key[:20]}...")
        print(f"🌐 Base URL: {app_settings.ai.OPENAI_BASE_URL}")
        print(f"🤖 Model: {app_settings.ai.OPENAI_MODEL}")
        
        # 初始化客户端
        client = OpenAI(
            api_key=api_key,
            base_url=app_settings.ai.OPENAI_BASE_URL
        )
        
        # 测试简单的对话
        print("\n🚀 发送测试请求...")
        response = client.chat.completions.create(
            model=app_settings.ai.OPENAI_MODEL,
            messages=[
                {"role": "system", "content": "你是一个测试助手。"},
                {"role": "user", "content": "请简单回复：测试成功"}
            ],
            max_tokens=50
        )
        
        result = response.choices[0].message.content
        print(f"✅ OpenAI官方API测试成功!")
        print(f"📝 响应内容: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI官方API测试失败: {e}")
        return False


def test_302ai_image_api():
    """测试302.ai图片生成API"""
    print("\n🎨 测试302.ai图片生成API...")
    print("=" * 50)
    
    try:
        # 获取API Key
        api_key = app_settings.ai.API_302_KEY
        if not api_key:
            print("❌ 未配置302.ai API Key")
            return False
        
        print(f"🔑 API Key: {api_key[:20]}...")
        print(f"🌐 Base URL: {app_settings.ai.API_302_BASE_URL}")
        
        url = f"{app_settings.ai.API_302_BASE_URL}/302/images/generations"
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "model": "hidream-i1-fast",
            "prompt": "A simple test image, blue circle",
            "n": 1,
            "size": "512x512",
            "response_format": "b64_json",
            "quality": "standard"
        }
        
        print("\n🚀 发送测试请求...")
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("data") and len(result["data"]) > 0:
                print("✅ 302.ai图片生成API测试成功!")
                print(f"📊 生成了 {len(result['data'])} 张图片")
                return True
            else:
                print("❌ 响应中无图片数据")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 302.ai图片生成API测试失败: {e}")
        return False


def test_ai_services():
    """测试AI服务类"""
    print("\n🔧 测试AI服务类...")
    print("=" * 50)
    
    try:
        # 测试基础AI服务
        from services.ai_service import AIService
        
        ai_service = AIService()
        if ai_service.is_available():
            print("✅ AIService 初始化成功")
        else:
            print("❌ AIService 初始化失败")
            return False
        
        # 测试增强AI服务
        from services.enhanced_ai_service import EnhancedAIService
        
        enhanced_service = EnhancedAIService()
        if enhanced_service.is_available():
            print("✅ EnhancedAIService 初始化成功")
        else:
            print("❌ EnhancedAIService 初始化失败")
            return False
        
        # 测试图片生成服务
        from services.ai_image_generation_service import AIImageGenerationService
        
        image_service = AIImageGenerationService()
        print("✅ AIImageGenerationService 初始化成功")
        print(f"🔑 使用的API Key: {image_service.api_key[:20]}...")
        print(f"🌐 使用的Base URL: {image_service.base_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI服务类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_configuration_summary():
    """显示配置摘要"""
    print("\n📋 当前配置摘要")
    print("=" * 60)
    
    print("🤖 OpenAI官方API配置:")
    openai_key = app_settings.ai.get_openai_api_key()
    print(f"   - API Key: {openai_key[:20] if openai_key else '未配置'}...")
    print(f"   - Base URL: {app_settings.ai.OPENAI_BASE_URL}")
    print(f"   - Model: {app_settings.ai.OPENAI_MODEL}")
    
    print("\n🎨 302.ai API配置:")
    print(f"   - API Key: {app_settings.ai.API_302_KEY[:20] if app_settings.ai.API_302_KEY else '未配置'}...")
    print(f"   - Base URL: {app_settings.ai.API_302_BASE_URL}")
    
    print("\n🔧 环境变量状态:")
    print(f"   - OPENAI_API_KEY: {'已设置' if os.getenv('OPENAI_API_KEY') else '未设置'}")
    print(f"   - API_302_KEY: {'已设置' if os.getenv('API_302_KEY') else '未设置'}")


def main():
    """主测试函数"""
    print("🚀 开始双API配置测试")
    print("=" * 60)
    
    # 显示配置摘要
    show_configuration_summary()
    
    test_results = []
    
    # 运行测试
    test_results.append(("AI服务类初始化", test_ai_services()))
    test_results.append(("OpenAI官方API", test_openai_official_api()))
    test_results.append(("302.ai图片生成API", test_302ai_image_api()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！双API配置正确。")
        print("\n💡 使用说明:")
        print("   - 队徽生成功能使用302.ai API")
        print("   - GPT对话、文本生成等功能使用OpenAI官方API")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
        print("\n🔧 配置建议:")
        print("   1. 确保环境变量 OPENAI_API_KEY 已设置（OpenAI官方）")
        print("   2. 确保环境变量 API_302_KEY 已设置（302.ai）")
        print("   3. 或者在 .streamlit/secrets.toml 中配置相应密钥")


if __name__ == "__main__":
    main()
