#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立诊断脚本 - 不依赖streamlit
Standalone Diagnosis Script - No Streamlit Dependencies
"""

import os
import json
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    exists = os.path.exists(file_path)
    status = "✅" if exists else "❌"
    print(f"{status} {description}: {file_path}")
    return exists

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载文件失败 {file_path}: {e}")
        return None

def diagnose_user_data():
    """诊断用户数据"""
    print("🔍 诊断1: 用户数据结构检查")
    print("=" * 40)
    
    user_id = "37d53472d725"
    team_name = "003222"
    base_path = f"data/user_{user_id}"
    
    print(f"用户ID: {user_id}")
    print(f"球队名称: {team_name}")
    print(f"基础路径: {base_path}")
    print()
    
    # 检查关键文件
    files = {
        "球队数据": f"{base_path}/teams/{team_name}.json",
        "AI数据": f"{base_path}/enhanced_ai_data/{team_name}_ai_data.json",
        "AI导出": f"{base_path}/exports/team_{team_name}_ai_ready.json",
        "工作流结果": f"{base_path}/fashion_workflow/workflow_{team_name}_20250824_141624.json",
        "Word输出目录": f"{base_path}/word_output"
    }
    
    file_status = {}
    for desc, path in files.items():
        file_status[desc] = check_file_exists(path, desc)
    
    return user_id, team_name, base_path, file_status

def analyze_workflow_result(base_path, team_name):
    """分析工作流结果"""
    print("\n🔍 诊断2: 工作流结果分析")
    print("=" * 40)
    
    workflow_file = f"{base_path}/fashion_workflow/workflow_{team_name}_20250824_141624.json"
    
    if not os.path.exists(workflow_file):
        print("❌ 工作流结果文件不存在")
        return None
    
    workflow_data = load_json_file(workflow_file)
    if not workflow_data:
        return None
    
    print("工作流基本信息:")
    print(f"   执行时间: {workflow_data.get('execution_time', 'N/A')}")
    print(f"   场景模式: {workflow_data.get('scenario', 'N/A')}")
    print(f"   处理球员数: {workflow_data.get('processed_players', 0)}")
    
    fashion_result = workflow_data.get('fashion_result', {})
    print(f"   换装成功: {fashion_result.get('success', False)}")
    print(f"   成功数量: {fashion_result.get('successful_count', 0)}")
    print(f"   失败数量: {fashion_result.get('failed_count', 0)}")
    
    # 检查是否有Word生成结果
    word_result = workflow_data.get('word_generation_result')
    if word_result:
        print("✅ 包含Word生成结果")
        print(f"   Word生成成功: {word_result.get('success', False)}")
    else:
        print("❌ 没有Word生成结果")
        print("   → 这说明Word生成步骤没有执行!")
    
    return workflow_data

def analyze_ai_data(base_path, team_name):
    """分析AI数据"""
    print("\n🔍 诊断3: AI数据分析")
    print("=" * 40)
    
    # 检查AI聊天数据
    ai_chat_file = f"{base_path}/enhanced_ai_data/{team_name}_ai_data.json"
    ai_export_file = f"{base_path}/exports/team_{team_name}_ai_ready.json"
    
    ai_chat_data = None
    ai_export_data = None
    
    if os.path.exists(ai_chat_file):
        print("✅ AI聊天数据存在")
        ai_chat_data = load_json_file(ai_chat_file)
        if ai_chat_data:
            extracted_info = ai_chat_data.get('extracted_info', {})
            print(f"   基本信息: {extracted_info.get('basic_info', {})}")
    else:
        print("❌ AI聊天数据不存在")
    
    if os.path.exists(ai_export_file):
        print("✅ AI导出数据存在")
        ai_export_data = load_json_file(ai_export_file)
    else:
        print("❌ AI导出数据不存在")
    
    return ai_chat_data, ai_export_data

def analyze_team_data(base_path, team_name):
    """分析球队数据"""
    print("\n🔍 诊断4: 球队数据分析")
    print("=" * 40)
    
    team_file = f"{base_path}/teams/{team_name}.json"
    
    if not os.path.exists(team_file):
        print("❌ 球队数据文件不存在")
        return None
    
    team_data = load_json_file(team_file)
    if not team_data:
        return None
    
    players = team_data.get('players', [])
    print(f"球员总数: {len(players)}")
    
    players_with_photos = 0
    for player in players:
        name = player.get('name', 'N/A')
        photo = player.get('photo', '')
        
        if photo:
            # 检查原始照片
            original_photo = f"{base_path}/photos/{team_name}/{photo}"
            # 检查处理后照片
            processed_photo = f"{base_path}/processed_photos/{team_name}/{player.get('id', '')}_fashion_final.png"
            
            original_exists = os.path.exists(original_photo)
            processed_exists = os.path.exists(processed_photo)
            
            if original_exists or processed_exists:
                players_with_photos += 1
            
            print(f"   球员 {name}:")
            print(f"     原始照片: {'✅' if original_exists else '❌'} {original_photo}")
            print(f"     处理照片: {'✅' if processed_exists else '❌'} {processed_photo}")
        else:
            print(f"   球员 {name}: 无照片")
    
    print(f"有照片的球员: {players_with_photos}/{len(players)}")
    return team_data

def simulate_readiness_check(base_path, team_name):
    """模拟准备状态检查"""
    print("\n🔍 诊断5: 模拟准备状态检查")
    print("=" * 40)
    
    # 检查AI数据
    ai_chat_file = f"{base_path}/enhanced_ai_data/{team_name}_ai_data.json"
    ai_export_file = f"{base_path}/exports/team_{team_name}_ai_ready.json"
    
    has_ai_data = os.path.exists(ai_chat_file) or os.path.exists(ai_export_file)
    print(f"AI数据存在: {'✅' if has_ai_data else '❌'}")
    
    if not has_ai_data:
        print("❌ 准备状态: 未就绪 (原因: no_ai_data)")
        print("   → 这就是为什么没有自动生成Word的原因!")
        return False
    
    # 检查球员照片
    team_file = f"{base_path}/teams/{team_name}.json"
    if not os.path.exists(team_file):
        print("❌ 球队数据不存在")
        return False
    
    team_data = load_json_file(team_file)
    if not team_data:
        return False
    
    players_with_photos = 0
    for player in team_data.get('players', []):
        photo = player.get('photo', '')
        if photo:
            photo_path = f"{base_path}/photos/{team_name}/{photo}"
            if os.path.exists(photo_path):
                players_with_photos += 1
    
    print(f"有照片的球员: {players_with_photos}")
    
    if players_with_photos == 0:
        print("❌ 准备状态: 未就绪 (原因: no_photos)")
        print("   → 这就是为什么没有自动生成Word的原因!")
        return False
    
    print("✅ 准备状态: 就绪")
    print("   → 应该会自动生成Word，但实际没有生成")
    print("   → 可能是执行路径问题!")
    return True

def main():
    """主诊断函数"""
    print("🚀 Word生成问题独立诊断")
    print("=" * 50)
    
    # 诊断1: 用户数据结构
    user_id, team_name, base_path, file_status = diagnose_user_data()
    
    # 诊断2: 工作流结果
    workflow_data = analyze_workflow_result(base_path, team_name)
    
    # 诊断3: AI数据
    ai_chat_data, ai_export_data = analyze_ai_data(base_path, team_name)
    
    # 诊断4: 球队数据
    team_data = analyze_team_data(base_path, team_name)
    
    # 诊断5: 模拟准备状态检查
    readiness_ok = simulate_readiness_check(base_path, team_name)
    
    # 总结诊断结果
    print("\n📊 诊断总结")
    print("=" * 50)
    
    if workflow_data:
        fashion_success = workflow_data.get('fashion_result', {}).get('success', False)
        fashion_count = workflow_data.get('fashion_result', {}).get('successful_count', 0)
        word_result = workflow_data.get('word_generation_result')
        
        print(f"换装执行: {'✅ 成功' if fashion_success else '❌ 失败'}")
        print(f"成功数量: {fashion_count}")
        print(f"Word生成: {'✅ 已执行' if word_result else '❌ 未执行'}")
        
        if fashion_success and fashion_count > 0 and not word_result:
            print("\n🎯 问题确认:")
            print("   换装成功但没有自动生成Word!")
            print("   原因: 执行路径走了手动模式而不是AI模式")
            
            print("\n💡 解决方案:")
            print("1. 修复AI数据加载逻辑")
            print("2. 确保readiness检查返回正确状态")
            print("3. 在手动模式中也添加自动Word生成")
    
    print(f"\n准备状态模拟: {'✅ 就绪' if readiness_ok else '❌ 未就绪'}")

if __name__ == "__main__":
    main()
