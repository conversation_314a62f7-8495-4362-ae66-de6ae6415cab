#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模板管理功能
Test Template Management

测试新的模板管理系统是否正常工作
"""

import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_template_service():
    """测试模板服务"""
    print("🧪 测试模板服务")
    print("=" * 50)
    
    try:
        from services.template_service import TemplateService
        
        # 测试无用户ID的情况
        print("📦 测试系统模板服务...")
        system_service = TemplateService()
        
        # 检查系统模板目录是否创建
        if os.path.exists("assets/default_clothes"):
            print("✅ 系统模板目录存在")
            
            # 检查子目录
            subdirs = ["football_jerseys", "casual_wear", "formal_wear"]
            for subdir in subdirs:
                path = os.path.join("assets/default_clothes", subdir)
                if os.path.exists(path):
                    print(f"✅ {subdir} 目录存在")
                else:
                    print(f"❌ {subdir} 目录不存在")
        else:
            print("❌ 系统模板目录不存在")
        
        # 测试有用户ID的情况
        print("\n👤 测试用户模板服务...")
        test_user_id = "test_user_template"
        user_service = TemplateService(test_user_id)
        
        # 检查用户模板目录是否创建
        from services.auth_service import AuthService
        auth_service = AuthService()
        user_data_path = auth_service.get_user_data_path(test_user_id)
        
        expected_paths = [
            os.path.join(user_data_path, "templates"),
            os.path.join(user_data_path, "templates", "team_templates"),
            os.path.join(user_data_path, "templates", "personal_templates")
        ]
        
        for path in expected_paths:
            if os.path.exists(path):
                print(f"✅ 用户模板目录存在: {os.path.basename(path)}")
            else:
                print(f"❌ 用户模板目录不存在: {path}")
        
        print("✅ 模板服务测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_manager_integration():
    """测试FileManager集成"""
    print("\n🧪 测试FileManager集成")
    print("=" * 50)
    
    try:
        from data.file_manager import FileManager
        
        # 测试有用户ID的FileManager
        test_user_id = "test_user_filemanager"
        file_manager = FileManager(test_user_id)
        
        # 检查user_id属性
        if hasattr(file_manager, 'user_id') and file_manager.user_id == test_user_id:
            print("✅ FileManager user_id 属性正确")
        else:
            print("❌ FileManager user_id 属性错误")
            return False
        
        # 测试save_template_image方法签名
        import inspect
        sig = inspect.signature(file_manager.save_template_image)
        params = list(sig.parameters.keys())
        
        expected_params = ['template_file', 'filename', 'team_name']
        if all(param in params for param in expected_params):
            print("✅ save_template_image 方法签名正确")
        else:
            print(f"❌ save_template_image 方法签名错误: {params}")
            return False
        
        print("✅ FileManager集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_service_folders():
    """测试AuthService文件夹创建"""
    print("\n🧪 测试AuthService文件夹创建")
    print("=" * 50)
    
    try:
        from services.auth_service import AuthService
        
        auth_service = AuthService()
        test_user_id = "test_user_auth"
        
        # 创建用户文件夹
        user_folder = auth_service.create_user_data_folder(test_user_id)
        print(f"✅ 用户文件夹创建: {user_folder}")
        
        # 检查所有必需的子文件夹
        expected_folders = [
            'teams', 'photos', 'exports', 'templates', 
            'processed_photos', 'word_output', 'temp_fashion'
        ]
        
        for folder in expected_folders:
            folder_path = os.path.join(user_folder, folder)
            if os.path.exists(folder_path):
                print(f"✅ {folder} 文件夹存在")
            else:
                print(f"❌ {folder} 文件夹不存在")
                return False
        
        # 检查templates子文件夹
        template_subfolders = ['team_templates', 'personal_templates']
        templates_path = os.path.join(user_folder, 'templates')
        
        for subfolder in template_subfolders:
            subfolder_path = os.path.join(templates_path, subfolder)
            if os.path.exists(subfolder_path):
                print(f"✅ templates/{subfolder} 文件夹存在")
            else:
                print(f"❌ templates/{subfolder} 文件夹不存在")
                return False
        
        print("✅ AuthService文件夹创建测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_selector_component():
    """测试模板选择器组件"""
    print("\n🧪 测试模板选择器组件")
    print("=" * 50)
    
    try:
        from components.template_selector import TemplateSelectorComponent
        
        # 测试组件初始化
        test_user_id = "test_user_selector"
        selector = TemplateSelectorComponent(test_user_id)
        
        # 检查组件属性
        if hasattr(selector, 'template_service'):
            print("✅ 模板选择器组件初始化成功")
        else:
            print("❌ 模板选择器组件初始化失败")
            return False
        
        # 检查方法存在
        required_methods = [
            'render_template_selector',
            'render_simple_template_upload',
            '_render_system_templates',
            '_render_user_templates',
            '_render_template_upload'
        ]
        
        for method in required_methods:
            if hasattr(selector, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法不存在")
                return False
        
        print("✅ 模板选择器组件测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n🧪 测试目录结构")
    print("=" * 50)
    
    # 检查系统模板目录结构
    system_template_structure = {
        "assets/default_clothes": ["football_jerseys", "casual_wear", "formal_wear"]
    }
    
    for base_dir, subdirs in system_template_structure.items():
        if os.path.exists(base_dir):
            print(f"✅ {base_dir} 存在")
            for subdir in subdirs:
                subdir_path = os.path.join(base_dir, subdir)
                if os.path.exists(subdir_path):
                    print(f"✅ {subdir_path} 存在")
                else:
                    print(f"❌ {subdir_path} 不存在")
                    return False
        else:
            print(f"❌ {base_dir} 不存在")
            return False
    
    print("✅ 目录结构测试通过！")
    return True

def main():
    """主测试函数"""
    print("🚀 开始模板管理系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行测试
    test_results.append(("目录结构", test_directory_structure()))
    test_results.append(("AuthService文件夹创建", test_auth_service_folders()))
    test_results.append(("模板服务", test_template_service()))
    test_results.append(("FileManager集成", test_file_manager_integration()))
    test_results.append(("模板选择器组件", test_template_selector_component()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！模板管理系统已就绪。")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
