#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word生成问题修复脚本
Fix Script for Word Generation Issues
"""

import os
import shutil
from pathlib import Path

def backup_original_files():
    """备份原始文件"""
    print("📦 备份原始文件")
    print("=" * 40)
    
    files_to_backup = [
        "services/fashion_workflow_service.py",
        "components/fashion_workflow.py"
    ]
    
    backup_dir = "backup_before_fix"
    os.makedirs(backup_dir, exist_ok=True)
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, os.path.basename(file_path))
            shutil.copy2(file_path, backup_path)
            print(f"✅ 备份: {file_path} → {backup_path}")
        else:
            print(f"❌ 文件不存在: {file_path}")

def fix_photo_path_logic():
    """修复照片路径逻辑"""
    print("\n🔧 修复1: 照片路径逻辑")
    print("=" * 40)
    
    file_path = "services/fashion_workflow_service.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 读取原文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找需要修复的代码段
    old_code = '''                    # 构建照片绝对路径
                    if player.get('photo'):
                        user_id = st.session_state.get('user_id', 'default_user')
                        photo_path = os.path.join('data', user_id, 'photos', player['photo'])
                        if os.path.exists(photo_path):
                            photo_info["absolute_path"] = os.path.abspath(photo_path)'''
    
    new_code = '''                    # 构建照片绝对路径
                    if player.get('photo'):
                        user_id = st.session_state.get('user_id', 'default_user')
                        # 修复: 照片路径应该包含team_name子目录
                        photo_path = os.path.join('data', user_id, 'photos', team_name, player['photo'])
                        if os.path.exists(photo_path):
                            photo_info["absolute_path"] = os.path.abspath(photo_path)'''
    
    if old_code in content:
        content = content.replace(old_code, new_code)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复照片路径逻辑: {file_path}")
        return True
    else:
        print(f"❌ 未找到目标代码段: {file_path}")
        return False

def add_word_generation_to_manual_mode():
    """在手动模式中添加Word生成"""
    print("\n🔧 修复2: 手动模式添加Word生成")
    print("=" * 40)
    
    file_path = "components/fashion_workflow.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 读取原文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找插入点 - 在手动模式成功后
    insert_point = '''                st.success("🎉 手动模式换装完成！")
                self._show_workflow_result(workflow_result)'''
    
    word_generation_code = '''                st.success("🎉 手动模式换装完成！")
                
                # 自动生成Word文档
                if fashion_result.get("success", False) and fashion_result.get("successful_count", 0) > 0:
                    st.info("📄 开始自动生成Word报名表...")
                    try:
                        # 获取AI数据用于Word生成
                        ai_data = self.workflow_service._load_ai_export_data(team_name)
                        if ai_data:
                            team_info = ai_data.get("team_info", {})
                            ai_extracted_info = team_info.get("ai_extracted_info", {})
                            basic_info = ai_extracted_info.get("basic_info", {})
                            
                            team_data = {
                                "name": team_name,
                                "leader": basic_info.get("leader_name", ""),
                                "coach": basic_info.get("contact_person", ""),
                                "doctor": basic_info.get("team_doctor", "")
                            }
                        else:
                            team_data = {"name": team_name}
                        
                        word_result = self.workflow_service._auto_generate_word_document(
                            team_name, player_photo_mapping, None
                        )
                        workflow_result["word_generation_result"] = word_result
                        
                        if word_result.get("success", False):
                            st.success("✅ Word报名表自动生成成功！")
                        else:
                            st.warning(f"⚠️ Word生成失败: {word_result.get('error', '未知错误')}")
                            
                    except Exception as e:
                        st.warning(f"⚠️ Word自动生成失败: {str(e)}")
                
                self._show_workflow_result(workflow_result)'''
    
    if insert_point in content:
        content = content.replace(insert_point, word_generation_code)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 添加手动模式Word生成: {file_path}")
        return True
    else:
        print(f"❌ 未找到插入点: {file_path}")
        return False

def add_word_generation_to_unified_mode():
    """在统一模式中添加Word生成"""
    print("\n🔧 修复3: 统一模式添加Word生成")
    print("=" * 40)
    
    file_path = "components/fashion_workflow.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 读取原文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找插入点 - 在统一模式成功后
    insert_point = '''                st.success("🎉 统一模式换装完成！")
                self._show_workflow_result(workflow_result)'''
    
    word_generation_code = '''                st.success("🎉 统一模式换装完成！")
                
                # 自动生成Word文档
                if fashion_result.get("success", False) and fashion_result.get("successful_count", 0) > 0:
                    st.info("📄 开始自动生成Word报名表...")
                    try:
                        # 获取AI数据用于Word生成
                        ai_data = self.workflow_service._load_ai_export_data(team_name)
                        if ai_data:
                            team_info = ai_data.get("team_info", {})
                            ai_extracted_info = team_info.get("ai_extracted_info", {})
                            basic_info = ai_extracted_info.get("basic_info", {})
                            
                            team_data = {
                                "name": team_name,
                                "leader": basic_info.get("leader_name", ""),
                                "coach": basic_info.get("contact_person", ""),
                                "doctor": basic_info.get("team_doctor", "")
                            }
                        else:
                            team_data = {"name": team_name}
                        
                        word_result = self.workflow_service._auto_generate_word_document(
                            team_name, player_photo_mapping, None
                        )
                        workflow_result["word_generation_result"] = word_result
                        
                        if word_result.get("success", False):
                            st.success("✅ Word报名表自动生成成功！")
                        else:
                            st.warning(f"⚠️ Word生成失败: {word_result.get('error', '未知错误')}")
                            
                    except Exception as e:
                        st.warning(f"⚠️ Word自动生成失败: {str(e)}")
                
                self._show_workflow_result(workflow_result)'''
    
    if insert_point in content:
        content = content.replace(insert_point, word_generation_code)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 添加统一模式Word生成: {file_path}")
        return True
    else:
        print(f"❌ 未找到插入点: {file_path}")
        return False

def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建测试脚本")
    print("=" * 40)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后测试脚本
Post-Fix Test Script
"""

import os
import json

def test_fixed_logic():
    """测试修复后的逻辑"""
    print("🧪 测试修复后的逻辑")
    print("=" * 40)
    
    user_id = "37d53472d725"
    team_name = "003222"
    
    # 模拟修复后的照片路径逻辑
    team_file = f"data/user_{user_id}/teams/{team_name}.json"
    
    if os.path.exists(team_file):
        with open(team_file, 'r', encoding='utf-8') as f:
            team_data = json.load(f)
        
        for player in team_data.get('players', []):
            name = player.get('name', 'N/A')
            photo = player.get('photo', '')
            
            if photo:
                # 修复后的路径逻辑
                photo_path = f"data/user_{user_id}/photos/{team_name}/{photo}"
                exists = os.path.exists(photo_path)
                print(f"球员 {name}: {photo_path} {'✅' if exists else '❌'}")
                
                if exists:
                    print(f"   → 修复成功！照片路径正确")
                else:
                    print(f"   → 照片文件不存在")
    
    print("\\n预期结果:")
    print("1. 照片路径解析正确")
    print("2. readiness检查返回True")
    print("3. 执行AI模式或手动模式都会自动生成Word")
    print("4. 换装完成后立即生成Word文档")

if __name__ == "__main__":
    test_fixed_logic()
'''
    
    with open("test_after_fix.py", 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 创建测试脚本: test_after_fix.py")

def main():
    """主修复函数"""
    print("🚀 Word生成问题修复")
    print("=" * 50)
    
    # 备份原始文件
    backup_original_files()
    
    # 修复1: 照片路径逻辑
    fix1_success = fix_photo_path_logic()
    
    # 修复2: 手动模式添加Word生成
    fix2_success = add_word_generation_to_manual_mode()
    
    # 修复3: 统一模式添加Word生成
    fix3_success = add_word_generation_to_unified_mode()
    
    # 创建测试脚本
    create_test_script()
    
    # 总结
    print("\n📊 修复总结")
    print("=" * 50)
    
    print(f"修复1 - 照片路径逻辑: {'✅ 成功' if fix1_success else '❌ 失败'}")
    print(f"修复2 - 手动模式Word生成: {'✅ 成功' if fix2_success else '❌ 失败'}")
    print(f"修复3 - 统一模式Word生成: {'✅ 成功' if fix3_success else '❌ 失败'}")
    
    if fix1_success and (fix2_success or fix3_success):
        print("\n🎉 修复完成！")
        print("预期效果:")
        print("1. 换装成功后自动生成Word文档")
        print("2. 所有执行路径都支持Word生成")
        print("3. 照片路径解析正确")
        
        print("\n下一步:")
        print("1. 运行 python test_after_fix.py 验证修复")
        print("2. 重新启动应用测试换装功能")
        print("3. 检查word_output目录是否生成文档")
    else:
        print("\n❌ 修复未完全成功")
        print("请检查错误信息并手动修复")

if __name__ == "__main__":
    main()
