#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit Word生成集成测试
测试在Streamlit环境中的Word生成功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 模拟Streamlit环境
class MockStreamlit:
    """模拟Streamlit的基本功能"""
    
    @staticmethod
    def error(msg):
        print(f"❌ ERROR: {msg}")
    
    @staticmethod
    def warning(msg):
        print(f"⚠️ WARNING: {msg}")
    
    @staticmethod
    def info(msg):
        print(f"ℹ️ INFO: {msg}")
    
    @staticmethod
    def success(msg):
        print(f"✅ SUCCESS: {msg}")

# 替换streamlit模块
sys.modules['streamlit'] = MockStreamlit()

def test_word_generator_component():
    """测试Word生成器组件"""
    print("🧪 测试Word生成器组件...")
    print("=" * 50)
    
    try:
        # 导入组件
        from components.word_generator import create_word_generator_component
        
        print("✅ Word生成器组件导入成功")
        
        # 创建组件实例
        word_generator = create_word_generator_component()
        print("✅ Word生成器组件创建成功")
        
        # 准备测试数据
        team_data = {
            'name': 'Streamlit测试球队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五'
        }
        
        players_data = [
            {
                'name': '测试球员1',
                'jersey_number': '10',
                'photo': '../word_zc/ai-football-generator/photos/player1.png'
            },
            {
                'name': '测试球员2', 
                'jersey_number': '9',
                'photo': '../word_zc/ai-football-generator/photos/player2.jpg'
            }
        ]
        
        print(f"✅ 测试数据准备完成 - 球队: {team_data['name']}, 球员: {len(players_data)}人")
        
        # 测试Word生成
        print("📄 测试Word生成功能...")
        
        if hasattr(word_generator, 'word_service') and word_generator.word_service:
            result = word_generator.word_service.generate_report(team_data, players_data)
            
            if result['success']:
                print("✅ Word报名表生成成功！")
                print(f"📁 文件路径: {result['file_path']}")
                
                # 检查文件是否存在
                if os.path.exists(result['file_path']):
                    file_size = os.path.getsize(result['file_path'])
                    print(f"📏 文件大小: {file_size / 1024:.1f} KB")
                    print("🎉 Streamlit集成测试完全成功！")
                    return True
                else:
                    print("❌ 生成的文件不存在")
                    return False
            else:
                print("❌ Word报名表生成失败")
                print(f"错误信息: {result['message']}")
                return False
        else:
            print("❌ Word生成服务不可用")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_chat_integration():
    """测试AI聊天组件中的Word生成集成"""
    print("\n🤖 测试AI聊天组件中的Word生成集成...")
    print("=" * 50)
    
    try:
        # 模拟session_state
        class MockSessionState:
            def __init__(self):
                self.data = {}
            
            def get(self, key, default=None):
                return self.data.get(key, default)
            
            def __setitem__(self, key, value):
                self.data[key] = value
            
            def __getitem__(self, key):
                return self.data[key]
            
            def __contains__(self, key):
                return key in self.data

        # 添加session_state到streamlit模块
        MockStreamlit.session_state = MockSessionState()
        
        # 导入AI聊天组件
        from components.ai_chat import AIChatComponent
        
        print("✅ AI聊天组件导入成功")
        
        # 创建组件实例（可能会有一些警告，这是正常的）
        ai_chat = AIChatComponent()
        print("✅ AI聊天组件创建成功")
        
        # 测试Word生成面板渲染（模拟）
        print("📄 测试Word生成面板...")
        
        # 检查是否有Word生成器
        if hasattr(ai_chat, 'word_generator'):
            print("✅ AI聊天组件包含Word生成器")
            return True
        else:
            print("⚠️ AI聊天组件暂未初始化Word生成器（需要调用_init_workflow_components）")
            
            # 尝试初始化
            ai_chat._init_workflow_components()
            
            if hasattr(ai_chat, 'word_generator') and ai_chat.word_generator:
                print("✅ Word生成器初始化成功")
                return True
            else:
                print("❌ Word生成器初始化失败")
                return False
            
    except Exception as e:
        print(f"❌ AI聊天集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 Streamlit Word生成集成测试套件")
    print("=" * 60)
    
    # 测试1: Word生成器组件
    test1_success = test_word_generator_component()
    
    # 测试2: AI聊天集成
    test2_success = test_ai_chat_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   Word生成器组件: {'✅ 成功' if test1_success else '❌ 失败'}")
    print(f"   AI聊天集成: {'✅ 成功' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有Streamlit集成测试通过！")
        print("💡 Word生成功能已成功集成到Streamlit应用中")
        print("\n📋 功能特性:")
        print("   ✅ Python ↔ Java Subprocess调用")
        print("   ✅ JSON数据传递")
        print("   ✅ Word文档生成")
        print("   ✅ 文件管理和下载")
        print("   ✅ AI聊天界面集成")
        print("   ✅ 错误处理和用户反馈")
        print("\n🚀 集成完成！用户现在可以:")
        print("   1. 在AI助手标签页中看到Word生成面板")
        print("   2. 一键生成专业的Word报名表")
        print("   3. 直接下载生成的文档")
        print("   4. 管理历史生成的文件")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
