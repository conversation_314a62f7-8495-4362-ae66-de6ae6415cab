#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI图片编辑器 - 独立应用
AI Photo Editor - Standalone Application

独立的AI修图工具，不依赖球队管理功能
"""

import streamlit as st
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入组件
from components.standalone_image_editor import StandaloneImageEditorComponent
from config.settings import app_settings


def configure_page():
    """配置页面设置"""
    st.set_page_config(
        page_title="AI图片编辑器",
        page_icon="🎨",
        layout="wide",
        initial_sidebar_state="collapsed"
    )


def render_header():
    """渲染页面头部"""
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        <div style="text-align: center; padding: 20px;">
            <h1>🎨 AI图片编辑器</h1>
            <p style="font-size: 18px; color: #666;">
                专业的AI驱动图片处理工具
            </p>
            <p style="font-size: 14px; color: #888;">
                支持AI换装、背景去除、白底添加等功能
            </p>
        </div>
        """, unsafe_allow_html=True)


def render_navigation():
    """渲染导航栏"""
    st.markdown("---")
    
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col1:
        if st.button("🏠 返回球队管理", use_container_width=True):
            st.switch_page("app.py")
    
    with col2:
        st.markdown(
            "<div style='text-align: center; padding: 10px;'>"
            "<strong>当前页面：AI图片编辑器</strong>"
            "</div>", 
            unsafe_allow_html=True
        )
    
    with col3:
        if st.button("ℹ️ 使用帮助", use_container_width=True):
            show_help()


def show_help():
    """显示使用帮助"""
    with st.expander("📖 使用帮助", expanded=True):
        st.markdown("""
        ### 🎯 功能介绍
        
        **AI图片编辑器**是一个独立的图片处理工具，提供以下功能：
        
        #### 🔄 AI换装
        - 使用AI技术将人物服装替换为模板样式
        - 需要上传模板图片作为参考
        - 适用于统一服装风格、虚拟试衣等场景
        
        #### 🖼️ 背景去除
        - 智能识别并移除图片背景
        - 生成透明背景的图片
        - 适用于证件照、产品图等场景
        
        #### ⚪ 添加白底
        - 为图片添加纯白色背景
        - 适用于证件照、商品图等需要统一背景的场景
        
        ### 📝 使用步骤
        
        1. **上传图片**：选择要处理的图片文件
        2. **选择处理方式**：勾选需要的处理功能
        3. **上传模板**：如果选择了换装功能，需要上传模板图
        4. **开始处理**：点击"开始处理"按钮
        5. **下载结果**：处理完成后下载结果图片
        
        ### 💡 使用提示
        
        - 支持 PNG、JPG、JPEG 格式
        - 建议图片大小不超过10MB
        - 可以同时选择多种处理方式
        - 处理时间根据图片大小和复杂度而定
        
        ### 🔒 隐私保护
        
        - 所有图片处理都在本地进行
        - 临时文件会自动清理
        - 不会保存或上传您的个人图片
        """)


def render_footer():
    """渲染页面底部"""
    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; color: #888; padding: 20px;'>"
        "AI图片编辑器 | 基于先进的AI技术 | "
        "<a href='#' style='color: #888;'>使用条款</a> | "
        "<a href='#' style='color: #888;'>隐私政策</a>"
        "</div>", 
        unsafe_allow_html=True
    )


def main():
    """主应用函数"""
    # 配置页面
    configure_page()
    
    # 渲染页面头部
    render_header()
    
    # 渲染导航栏
    render_navigation()
    
    # 主要内容区域
    st.markdown("---")
    
    # 创建并渲染图片编辑器组件
    editor_component = StandaloneImageEditorComponent()
    editor_component.render_editor_interface()
    
    # 渲染页面底部
    render_footer()


if __name__ == "__main__":
    main()
